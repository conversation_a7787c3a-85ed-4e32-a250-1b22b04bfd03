# D:\school_fees_saas_v2\apps\hr\views.py

from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, FormView
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib import messages
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError
from decimal import Decimal # For LeaveBalance calculations

from django.views.generic import ListView, DetailView
from django.db.models import Sum # <<< ADD THIS IMPORT
from decimal import Decimal      # <<< ADD THIS IMPORT

from apps.common.mixins import TenantPermissionRequiredMixin

# --- Authentication & Permissions ---
import logging
from django.urls import reverse_lazy, reverse
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.contrib.auth.decorators import login_required # Not used for CBVs here
from apps.common.mixins import StaffLoginRequiredMixin

# --- Models ---
from .models import EmployeeProfile, LeaveType, LeaveBalance, LeaveRequest
from apps.schools.models import StaffUser # Needed for type checking request.user

# --- Forms ---
from .forms import (
    StaffLeaveRequestForm,
    AdminLeaveRequestUpdateForm,
    # LeaveBalanceForm, # Uncomment if you create this form
)
from .models import LeaveType, LeaveRequest
from .forms import LeaveTypeForm, StaffLeaveRequestForm
from .services import PayrollCalculationService

# --- THIS IS WHERE THE FIX GOES ---
# You are already importing other forms, just add PayrollPeriodForm to the list
from .forms import (
    SalaryComponentForm, 
    TaxBracketForm,
    # SalaryGradeForm, # Import the form for managing grades
    # SalaryGradeComponentFormSet, # Import the correct formset for grade components
    # StaffSalaryForm, # The simple form for assigning a grade/basic salary
    PayrollPeriodForm
)

from .models import StaffSalary


# Import your models
from .models import SalaryComponent, TaxBracket #, StaffSalary
from apps.schools.models import StaffUser

logger = logging.getLogger(__name__)


# ========================================
# --- Leave Type CRUD Views ---
# ========================================

# D:\school_fees_saas_v2\apps\hr\views.py
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.contrib import messages

from .models import LeaveType, LeaveRequest # Add LeaveBalance, StaffUser later
# from .forms import LeaveTypeForm # Add LeaveRequestForm later
# from apps.schools.models import StaffUser # If needed for requestor info

# --- LeaveType CRUD ---
class LeaveTypeListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = LeaveType
    template_name = 'hr/leavetype_list.html'
    context_object_name = 'leave_types'
    permission_required = 'hr.view_leavetype' # Ensure these permissions exist
    login_url = reverse_lazy('schools:staff_login') # Or your tenant login

    def get_queryset(self):
        # Ensures data is fetched from the current tenant's schema
        return LeaveType.objects.all().order_by('name')

class LeaveTypeCreateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = LeaveType
    form_class = LeaveTypeForm
    template_name = 'hr/leavetype_form.html'
    permission_required = 'hr.add_leavetype'
    success_url = reverse_lazy('hr:leavetype_list')
    success_message = "Leave Type '%(name)s' created successfully."
    login_url = reverse_lazy('schools:staff_login')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Create New Leave Type"
        context['form_mode'] = "create"
        return context

class LeaveTypeUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = LeaveType
    form_class = LeaveTypeForm
    template_name = 'hr/leavetype_form.html'
    permission_required = 'hr.change_leavetype'
    success_url = reverse_lazy('hr:leavetype_list')
    success_message = "Leave Type '%(name)s' updated successfully."
    login_url = reverse_lazy('schools:staff_login')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Update Leave Type: {self.object.name}"
        context['form_mode'] = "update"
        return context

class LeaveTypeDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    model = LeaveType
    template_name = 'hr/leavetype_confirm_delete.html'
    permission_required = 'hr.delete_leavetype'
    success_url = reverse_lazy('hr:leavetype_list')
    login_url = reverse_lazy('schools:staff_login')

    def post(self, request, *args, **kwargs):
        # Add success message manually for DeleteView
        # Check if it has associated leave requests first, ideally
        leave_type_name = self.get_object().name
        if LeaveRequest.objects.filter(leave_type=self.get_object()).exists():
            messages.error(request, f"Cannot delete '{leave_type_name}' as it has associated leave requests.")
            return redirect(self.success_url)
        
        messages.success(request, f"Leave Type '{leave_type_name}' deleted successfully.")
        return super().post(request, *args, **kwargs)

# --- END LeaveType CRUD ---

# ========================================
# --- Leave Request Views ---
# ========================================

import logging
from .models import LeaveRequest, EmployeeProfile

logger = logging.getLogger(__name__)

class StaffLeaveRequestListView(StaffLoginRequiredMixin, ListView):
    model = LeaveRequest
    template_name = 'hr/staff_leaverequest_list.html'
    context_object_name = 'leave_requests'
    paginate_by = 10

    def get_queryset(self):
        """
        Fetches leave requests only for the currently logged-in staff member.
        """
        user = self.request.user

        try:
            # Access the employee profile using the related_name 'hr_profile'
            employee_profile = user.hr_profile
            return LeaveRequest.objects.filter(employee=employee_profile).select_related('leave_type').order_by('-start_date')
        except EmployeeProfile.DoesNotExist:
            messages.error(self.request, "Your employee profile is not set up correctly to view leave requests. Please contact an administrator.")
            return LeaveRequest.objects.none()
        except Exception as e:
            messages.error(self.request, f"An error occurred while accessing your profile: {e}")
            return LeaveRequest.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "My Leave Requests"
        return context
    
    
    
# class StaffLeaveRequestCreateView(LoginRequiredMixin, SuccessMessageMixin, CreateView):
#     model = LeaveRequest
#     form_class = StaffLeaveRequestForm
#     template_name = 'hr/staff_leaverequest_form.html'
#     success_url = reverse_lazy('hr:staff_leaverequest_list')
#     success_message = "Your leave request has been submitted successfully."
#     login_url = reverse_lazy('schools:staff_login')

#     def get_form_kwargs(self):
#         kwargs = super().get_form_kwargs()
#         kwargs['user'] = self.request.user # Pass current user to the form
#         return kwargs

#     def form_valid(self, form):
#         form.instance.staff = self.request.user # Assign staff member making the request
#         # Optionally set manager if you have that logic (e.g., from staff profile)
#         # form.instance.approved_by = ... (leave as None initially)
#         return super().form_valid(form)

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = "Apply for Leave"
#         return context

# D:\school_fees_saas_v2\apps\hr\views.py

from .utils import get_employee_profile_for_staff # <<< IMPORT THE NEW HELPER
from .forms import StaffLeaveRequestForm
from .models import LeaveRequest


# D:\school_fees_saas_v2\apps\hr\views.py

class StaffLeaveRequestCreateView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = LeaveRequest
    form_class = StaffLeaveRequestForm
    template_name = 'hr/staff_leaverequest_form.html'
    success_url = reverse_lazy('hr:staff_leaverequest_list')
    success_message = "Your leave request has been submitted successfully."

    permission_required = 'hr.add_leaverequest'

    def dispatch(self, request, *args, **kwargs):
        """
        Get the employee profile once and store it on the view instance.
        This runs for both GET and POST requests.
        """
        # get_employee_profile_for_staff is your utility function.
        # This check ensures we only proceed if a profile exists.
        self.employee_profile = get_employee_profile_for_staff(request.user)
        if not self.employee_profile:
            messages.error(request, _("Cannot process request: Your employee profile could not be found. Please contact HR."))
            return redirect('schools:dashboard') # Redirect to a safe page

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        """
        Pass the employee profile to the form's __init__ method.
        """
        kwargs = super().get_form_kwargs()
        # The form can use this profile for validation (e.g., checking leave balances).
        kwargs['employee_profile'] = self.employee_profile 
        return kwargs

    def form_valid(self, form):
        """
        This method is called when valid form data has been POSTed.
        """
        # --- THIS IS THE FIX ---
        # Access the related StaffUser via the 'user' attribute, not 'staff_user'
        logger.info(f"StaffLeaveRequestCreateView.form_valid: Form is valid for {self.employee_profile.user.email}.")
        
        # This part should already be correct from our previous fix
        leave_request = form.save(commit=False)
        leave_request.employee = self.employee_profile
        leave_request.save()
        
        # The SuccessMessageMixin will handle the success message.
        messages.success(self.request, "Your leave request has been submitted successfully.") # Or use the mixin's message
        
        return super().form_valid(form)
    

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Apply for Leave")
        # Pass the profile to the template if it needs to display any profile info.
        context['employee_profile'] = self.employee_profile
        return context
    


class StaffLeaveRequestDetailView(LoginRequiredMixin, DetailView):
    model = LeaveRequest
    template_name = 'hr/staff_leaverequest_detail.html'
    context_object_name = 'leave_request'
    login_url = reverse_lazy('schools:staff_login')

    def get_queryset(self):
        # Staff can only see their own leave requests
        # return LeaveRequest.objects.filter(staff=self.request.user)
        return LeaveRequest.objects.filter(employee__user=self.request.user)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = _("Leave Request Details")
        return context
        
class StaffLeaveRequestCancelView(LoginRequiredMixin, SuccessMessageMixin, UpdateView):
    model = LeaveRequest
    fields = [] # No fields to edit, just changing status
    template_name = 'hr/staff_leaverequest_cancel_confirm.html' # Confirmation template
    success_url = reverse_lazy('hr:staff_leaverequest_list')
    success_message = "Your leave request has been cancelled."
    login_url = reverse_lazy('schools:staff_login')

    def get_queryset(self):
        # Staff can only cancel their own PENDING leave requests
        return LeaveRequest.objects.filter(staff=self.request.user, status=LeaveRequest.LeaveStatus.PENDING)

    def form_valid(self, form):
        leave_request = form.save(commit=False)
        if leave_request.status == LeaveRequest.LeaveStatus.PENDING:
            leave_request.status = LeaveRequest.LeaveStatus.CANCELLED_BY_STAFF
            leave_request.status_changed_at = timezone.now()
            leave_request.status_reason = "Cancelled by staff member."
            leave_request.save()
        else:
            messages.error(self.request, "This leave request cannot be cancelled as it's no longer pending.")
            return redirect(self.success_url) # Or to detail view
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.object.status != LeaveRequest.LeaveStatus.PENDING:
            context['can_cancel'] = False
        else:
            context['can_cancel'] = True
        return context

# --- END Staff Leave Request Views ---


# ========================================
# --- Leave Admin Views ---
# ========================================

class AdminLeaveRequestListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = LeaveRequest
    template_name = 'hr/admin_leaverequest_list.html'
    context_object_name = 'leave_requests'
    permission_required = 'hr.view_leaverequest'
    login_url = reverse_lazy('schools:staff_login')
    paginate_by = 20

    def get_queryset(self):
        queryset = LeaveRequest.objects.select_related(
            'employee__user', 'leave_type', 'approved_by__user'
        ).order_by('status', '-created_at')

        # Apply filters
        employee = self.request.GET.get('employee')
        if employee:
            queryset = queryset.filter(employee_id=employee)

        leave_type = self.request.GET.get('leave_type')
        if leave_type:
            queryset = queryset.filter(leave_type_id=leave_type)

        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['employees'] = EmployeeProfile.objects.select_related('user').order_by('user__last_name')
        context['leave_types'] = LeaveType.objects.all().order_by('name')
        return context





class LeaveRequestAdminUpdateView(LoginRequiredMixin, PermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = LeaveRequest
    form_class = AdminLeaveRequestUpdateForm
    template_name = 'hr/leave_request_admin_update_form.html'
    success_url = reverse_lazy('hr:leave_request_list') # Or a dedicated admin list
    success_message = "Leave request status updated."
    context_object_name = 'leave_request'
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'hr.change_leaverequest' # Permission to approve/reject

    def form_valid(self, form):
        leave_request = form.save(commit=False)
        admin_user = self.request.user

        # Ensure admin_user has an EmployeeProfile to be an approver
        # This might not always be true (e.g. platform superuser vs. tenant staff admin)
        # For now, assume admin performing action has an hr_profile
        if not isinstance(admin_user, StaffUser) or not hasattr(admin_user, 'hr_profile'):
            messages.error(self.request, "Action requires the approver to have a staff profile.")
            return self.form_invalid(form)

        original_status = LeaveRequest.objects.get(pk=leave_request.pk).status
        new_status = leave_request.status

        leave_request.approved_by = admin_user.hr_profile
        leave_request.approval_date = timezone.now() if new_status in ['APPROVED', 'REJECTED', 'CANCELLED_BY_ADMIN'] else None

        try:
            with transaction.atomic():
                response = super().form_valid(form) # Saves the leave_request

                # Update LeaveBalance
                employee_profile = leave_request.employee
                leave_type = leave_request.leave_type
                days_requested = leave_request.duration

                balance, created = LeaveBalance.objects.get_or_create(
                    employee=employee_profile,
                    leave_type=leave_type,
                    # academic_year=... # If balances are yearly
                    defaults={'days_taken': Decimal('0.00'), 'days_accrued': leave_type.default_annual_days or Decimal('0.00')}
                )

                if new_status == 'APPROVED' and original_status != 'APPROVED':
                    balance.days_taken = (balance.days_taken or Decimal('0.00')) + days_requested
                    messages.info(self.request, f"Leave balance updated: {days_requested} days deducted for {employee_profile.user.full_name}.")
                elif original_status == 'APPROVED' and new_status != 'APPROVED': # Reverting an approval
                    balance.days_taken = (balance.days_taken or Decimal('0.00')) - days_requested
                    messages.info(self.request, f"Leave balance reverted: {days_requested} days added back for {employee_profile.user.full_name}.")
                
                balance.save()
                # TODO: Send notification to staff about status change
            return response
        except Exception as e:
            messages.error(self.request, f"Error updating leave request or balance: {e}")
            return self.form_invalid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f'Admin Update Leave: {self.object.employee.user.full_name} ({self.object.leave_type.name})'
        return context


# D:\school_fees_saas_v2\apps\hr\views.py
from django.views.generic import ListView
from django.views.generic.edit import UpdateView
from .models import LeaveBalance
from .forms import LeaveBalanceForm # You created this form in the last step

class LeaveBalanceListView(TenantPermissionRequiredMixin, ListView):
    model = LeaveBalance
    template_name = 'hr/leavebalance_list.html'
    context_object_name = 'leave_balances'
    permission_required = 'hr.view_leavebalance' # Assumes default permission exists
    paginate_by = 25

    def get_queryset(self):
        return LeaveBalance.objects.select_related(
            'employee__user', 'leave_type'
        ).order_by('employee__user__last_name', 'leave_type__name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Manage Staff Leave Balances"
        return context

class LeaveBalanceUpdateView(TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = LeaveBalance
    form_class = LeaveBalanceForm
    template_name = 'hr/leavebalance_form.html'
    permission_required = 'hr.change_leavebalance'
    success_url = reverse_lazy('hr:leave_balance_list')
    success_message = "Leave balance was updated successfully."

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Leave Balance for {self.object.employee.user.get_full_name()}"
        return context



# D:\school_fees_saas_v2\apps\hr\views.py

from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

# Import all necessary models at the top
from apps.schools.models import StaffUser
from .models import EmployeeProfile, LeaveBalance, LeaveRequest

@login_required(login_url='schools:staff_login')
def my_leave_summary_view(request):
    """
    Displays a summary of the logged-in staff member's leave balances
    and their recent leave requests.
    """
    if not isinstance(request.user, StaffUser):
        messages.error(request, _("Access denied. This page is for staff members only."))
        return redirect('schools:dashboard')

    staff_user = request.user
    
    try:
        employee_profile = staff_user.hr_profile
    except EmployeeProfile.DoesNotExist:
        messages.warning(request, _("Your employee profile has not been set up yet. Leave information is unavailable."))
        context = { 'view_title': _("My Leave Summary"), 'profile_missing': True }
        return render(request, 'hr/my_leave_summary.html', context)

    current_year = timezone.now().year

    # --- THIS IS THE CORRECTED QUERY ---
    # Use the correct field name 'year' to filter
    leave_balances = LeaveBalance.objects.filter(
        employee=employee_profile,
        year=current_year
    ).select_related('leave_type').order_by('leave_type__name')
    
    # This query for recent requests is already correct
    recent_requests = LeaveRequest.objects.filter(
        employee=employee_profile
    ).select_related('leave_type').order_by('-created_at')[:10]
    
    context = {
        'view_title': _("My Leave Summary"),
        'leave_balances': leave_balances,
        'recent_requests': recent_requests,
        'employee_profile': employee_profile,
    }
    
    return render(request, 'hr/my_leave_summary.html', context)


# # D:\school_fees_saas_v2\apps\hr\views.py


from .payroll import PayrollProcessor
from .models import Payslip, PayslipLineItem, StaffUser

class GeneratePayrollView(TenantPermissionRequiredMixin, FormView):
    template_name = 'hr/generate_payroll_form.html'
    form_class = PayrollPeriodForm # A simple form with month/year fields
    success_url = reverse_lazy('hr:payslip_list') # Redirect to the list of generated payslips
    permission_required = 'hr.add_payslip' # Or a custom 'can_run_payroll' permission

    def form_valid(self, form):
        pay_period_start = form.cleaned_data['pay_period_start']
        pay_period_end = form.cleaned_data['pay_period_end']
        
        active_staff = StaffUser.objects.filter(is_active=True)
        generated_count = 0
        
        for staff in active_staff:
            # Check if a payslip for this period already exists
            if Payslip.objects.filter(staff_user=staff, pay_period_start=pay_period_start).exists():
                logger.info(f"Payslip for {staff.email} for this period already exists. Skipping.")
                continue

            processor = PayrollProcessor(staff, pay_period_start, pay_period_end)
            result = processor.run()
            
            if result:
                with transaction.atomic():
                    # Create the main Payslip record
                    payslip = Payslip.objects.create(
                        staff_user=staff,
                        pay_period_start=pay_period_start,
                        pay_period_end=pay_period_end,
                        gross_earnings=result['gross_earnings'],
                        total_deductions=result['total_deductions'],
                        net_pay=result['net_pay'],
                        status=Payslip.PayslipStatus.GENERATED
                    )
                    
                    # Create the line items
                    line_items = []
                    for item in result['earnings_lines']:
                        line_items.append(PayslipLineItem(
                            payslip=payslip, name=item['name'], type='EARNING', 
                            amount=item['amount'], source_component=item.get('source')
                        ))
                    for item in result['deductions_lines']:
                        line_items.append(PayslipLineItem(
                            payslip=payslip, name=item['name'], type='DEDUCTION', 
                            amount=item['amount'], source_component=item.get('source')
                        ))
                    
                    PayslipLineItem.objects.bulk_create(line_items)
                    generated_count += 1
        
        messages.success(self.request, f"Successfully generated {generated_count} payslips for the selected period.")
        return super().form_valid(form)
    


# D:\school_fees_saas_v2\apps\hr\views.py

from django.shortcuts import redirect
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.db import transaction
from django.contrib import messages
from django.contrib.messages.views import SuccessMessageMixin

from .models import SalaryGrade
# from .forms import SalaryGradeForm, SalaryGradeComponentFormSet
from apps.common.mixins import TenantPermissionRequiredMixin

# ... your other existing views ...


# ==============================================================================
# SALARY GRADE CRUD VIEWS
# ==============================================================================

class SalaryGradeListView(TenantPermissionRequiredMixin, ListView):
    model = SalaryGrade
    template_name = 'hr/salarygrade_list.html'
    context_object_name = 'grades'
    permission_required = 'hr.view_salarygrade'
    paginate_by = 20

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Salary Grades"
        return context



class SalaryGradeDeleteView(TenantPermissionRequiredMixin, SuccessMessageMixin, DeleteView):
    model = SalaryGrade
    template_name = 'hr/confirm_delete_grade.html' # Use a specific delete template
    permission_required = 'hr.delete_salarygrade'
    success_url = reverse_lazy('hr:salarygrade_list')
    
    def get_success_message(self, cleaned_data):
        return f"Salary Grade '{self.object.name}' was deleted successfully."

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Delete Salary Grade"
        context['cancel_url'] = self.success_url
        return context
    
    
    
# D:\school_fees_saas_v2\apps\hr\views.py

from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, FormView
from django.db import transaction
from django.contrib import messages
from django.contrib.messages.views import SuccessMessageMixin

from .models import SalaryComponent, TaxBracket, SalaryGrade
from .forms import (
    SalaryComponentForm, TaxBracketForm,  
    PayrollPeriodForm
)
from apps.common.mixins import TenantPermissionRequiredMixin
from apps.schools.models import StaffUser


# ==============================================================================
# SALARY COMPONENT CRUD VIEWS
# ==============================================================================
class SalaryComponentListView(TenantPermissionRequiredMixin, ListView):
    model = SalaryComponent
    template_name = 'hr/salarycomponent_list.html'
    context_object_name = 'components'
    permission_required = 'hr.view_salarycomponent'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Salary Components (Earnings & Deductions)"
        return context

class SalaryComponentCreateView(TenantPermissionRequiredMixin, SuccessMessageMixin, CreateView):
    model = SalaryComponent
    form_class = SalaryComponentForm
    template_name = 'hr/form_generic.html' # Use a generic form template
    permission_required = 'hr.add_salarycomponent'
    success_url = reverse_lazy('hr:salarycomponent_list')
    success_message = "Component '%(name)s' was created successfully."
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Create New Salary Component"
        context['cancel_url'] = self.success_url
        return context

class SalaryComponentUpdateView(TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = SalaryComponent
    form_class = SalaryComponentForm
    template_name = 'hr/form_generic.html'
    permission_required = 'hr.change_salarycomponent'
    success_url = reverse_lazy('hr:salarycomponent_list')
    success_message = "Component '%(name)s' was updated successfully."

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Component: {self.object.name}"
        context['cancel_url'] = self.success_url
        return context

# ==============================================================================
# TAX BRACKET CRUD VIEWS (Follows the same pattern)
# ==============================================================================
class TaxBracketListView(TenantPermissionRequiredMixin, ListView):
    model = TaxBracket
    template_name = 'hr/taxbracket_list.html'
    permission_required = 'hr.view_taxbracket'
    # ...

class TaxBracketCreateView(TenantPermissionRequiredMixin, CreateView):
    model = TaxBracket
    form_class = TaxBracketForm
    template_name = 'hr/form_generic.html'
    permission_required = 'hr.add_taxbracket'
    success_url = reverse_lazy('hr:taxbracket_list')
    # ...


# --- THIS IS THE MISSING VIEW TO ADD ---
class TaxBracketUpdateView(TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = TaxBracket
    form_class = TaxBracketForm
    template_name = 'hr/form_generic.html' # Reusing the same generic form
    permission_required = 'hr.change_taxbracket'
    success_url = reverse_lazy('hr:taxbracket_list')
    success_message = "Tax Bracket '%(name)s' updated successfully."

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Tax Bracket: {self.object.name}"
        context['cancel_url'] = self.success_url
        return context
# --- END OF MISSING VIEW ---


# ==============================================================================
# SALARY GRADE CRUD VIEWS (Handles formset)
# ==============================================================================
class SalaryGradeListView(TenantPermissionRequiredMixin, ListView):
    model = SalaryGrade
    template_name = 'hr/salarygrade_list.html'
    permission_required = 'hr.view_salarygrade'
    # ...


# ==============================================================================
# STAFF SALARY ASSIGNMENT VIEW
# # ==============================================================================

# D:\school_fees_saas_v2\apps\hr\views.py

from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.views import View
from .models import Payslip
from apps.common.utils import render_to_pdf, PDF_AVAILABLE

from apps.schools.models import SchoolProfile 

from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin

class PayslipPDFView(StaffLoginRequiredMixin, View):
    """
    Generates and serves a PDF for a single payslip.
    Security: Staff can only view their own payslip unless they have a specific permission.
    """
    def get(self, request, *args, **kwargs):
        if not PDF_AVAILABLE:
            return HttpResponse("PDF generation is not available.", status=501)

        try:
            payslip = get_object_or_404(
                Payslip.objects.select_related(
                    'staff_member', 'payroll_run', 'staff_member__hr_profile'
                ), 
                pk=self.kwargs.get('pk')
            )
        except Payslip.DoesNotExist:
            return HttpResponse("Payslip not found.", status=404)

        # --- SECURITY CHECK ---
        # A user can see their own payslip.
        # A user with 'hr.manage_payroll' permission can see anyone's payslip.
        if not (request.user == payslip.staff_member or request.user.has_perm('hr.manage_payroll')):
            messages.error(request, "You do not have permission to view this payslip.")
            # Redirect to a safe page, like the HR dashboard or their own payslip list
            return redirect(reverse('hr:staff_payslip_list')) # Assuming this URL exists

        # --- CORRECTED WAY TO FETCH SCHOOL PROFILE ---
        # When inside a tenant context, this query is automatically scoped to that tenant's schema.
        # Since there should only be one profile per school, .first() is safe and efficient.
        
        
        print("\n--- [DEBUG] PAYSLIP PDF VIEW ---")
        school_profile = SchoolProfile.objects.first()
        if school_profile:
            print(f"✅ Found SchoolProfile object. PK: {school_profile.pk}")
            if school_profile.logo:
                print(f"✅ Logo field has a value.")
                print(f"   -> school_profile.logo.url: {school_profile.logo.url}")
                print(f"   -> school_profile.logo.path: {school_profile.logo.path}")
            else:
                print("❌ Logo field is EMPTY. No image has been uploaded for this profile.")
        else:
            print("❌ CRITICAL: No SchoolProfile object found for this tenant. It is None.")
        print("-------------------------------\n")
        
        # school_profile = SchoolProfile.objects.first()
        # if not school_profile:
        #     logger.warning(f"No SchoolProfile found for tenant {request.tenant.schema_name} when generating payslip PDF.")
        #     # You could return an error or let the template handle a None value
        #     # return HttpResponse("School profile not configured for this tenant.", status=500)

        context = {
            'payslip': payslip,
            'school_profile': school_profile, # Use the object we just fetched
            'tenant': request.tenant,
        }
        
        pdf = render_to_pdf('hr/pdf/payslip_pdf_template.html', context)
        
        
        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"Payslip_{payslip.payroll_run.pay_period_start.strftime('%b_%Y')}_{payslip.staff_member.last_name}.pdf"
            content = f"inline; filename={filename}"
            response['Content-Disposition'] = content
            return response
            
        return HttpResponse("Failed to generate PDF.", status=500)
    


# D:\school_fees_saas_v2\apps\hr\views.py

import logging # Make sure logging is imported
from django.views.generic import ListView
from .models import PayrollRun
from apps.common.mixins import TenantPermissionRequiredMixin

logger = logging.getLogger(__name__) # Set up logger for this file

class PayrollRunListView(TenantPermissionRequiredMixin, ListView):
    model = PayrollRun
    template_name = 'hr/payrollrun_list.html'
    context_object_name = 'payroll_runs'
    permission_required = 'hr.manage_payroll'
    paginate_by = 20


    def get_queryset(self):
        view_type = self.request.GET.get('view', 'outstanding')
        
        if view_type == 'completed':
            # Show PAID and CANCELLED runs in history
            queryset = PayrollRun.objects.filter(status__in=['PAID', 'CANCELLED'])
        else:
            # Default view is "Outstanding", which are PROCESSED runs
            queryset = PayrollRun.objects.filter(status='PROCESSED')
            
        return queryset.order_by('-payment_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Payroll Management"
        # Add a count for the "Outstanding" tab badge
        context['outstanding_run_count'] = PayrollRun.objects.filter(status='PROCESSED').count()
        return context
    
    # def get_queryset(self):
    #     """
    #     Fetches all payroll runs for the current tenant.
    #     """
    #     logger.info(f"--- [PayrollRunListView] get_queryset called for tenant '{self.request.tenant.schema_name}' ---")
        
    #     # Fetch ALL objects without any filters for debugging
    #     queryset = PayrollRun.objects.all().order_by('-payment_date')
        
    #     count = queryset.count()
    #     logger.info(f"--- [PayrollRunListView] Found {count} PayrollRun object(s) in the database for this tenant. ---")
        
    #     if count > 0:
    #         logger.info(f"--- [PayrollRunListView] First object found: {queryset.first()}")

    #     return queryset

    # def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Payroll Runs"
        return context
    


class PayrollRunDetailView(TenantPermissionRequiredMixin, DetailView):
    model = PayrollRun
    template_name = 'hr/payrollrun_detail.html'
    context_object_name = 'payroll_run'
    permission_required = 'hr.manage_payroll'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # self.object is the PayrollRun instance, provided by DetailView
        
        # Fetch the related payslips for this run.
        # select_related is used for performance to pre-fetch staff member data.
        payslips = self.object.payslips.all().select_related('staff_member')
        context['payslips'] = payslips
        
        # Calculate summary statistics for the dashboard cards
        context['payslip_count'] = payslips.count()
        context['total_net_pay'] = payslips.aggregate(total=Sum('net_pay'))['total'] or Decimal('0.00')
        context['total_gross_earnings'] = payslips.aggregate(total=Sum('gross_earnings'))['total'] or Decimal('0.00')
        context['total_deductions'] = payslips.aggregate(total=Sum('total_deductions'))['total'] or Decimal('0.00')
        
        context['view_title'] = f"Details for {self.object}"
        return context
    


# apps/hr/views.py

import logging
from datetime import date
from decimal import Decimal
from calendar import monthrange

from django.db import transaction
from django.urls import reverse_lazy
from django.utils import timezone
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import FormView

# Your project's models and forms
from apps.schools.models import StaffUser
from .models import StaffUser, PayrollRun, Payslip, StaffSalaryStructure
from .forms import ProcessPayrollForm

# --- The NEW service we are integrating ---
from .services import PayrollCalculationService

# Get an instance of a logger
logger = logging.getLogger(__name__)

# D:\school_fees_saas_v2\apps\hr\views.py

import logging
from decimal import Decimal
from datetime import date
from calendar import monthrange

from django.db import transaction
from django.urls import reverse_lazy
from django.contrib import messages
from django.views.generic.edit import FormView
from django.utils import timezone

from apps.common.mixins import TenantPermissionRequiredMixin
from .forms import ProcessPayrollForm
from .models import (
    PayrollRun, Payslip, StaffSalary, StaffUser, 
    SalaryComponent, GradeRule # Make sure all are imported
)

logger = logging.getLogger(__name__)

class ProcessPayrollView(TenantPermissionRequiredMixin, FormView):
    template_name = 'hr/process_payroll_form.html'
    form_class = ProcessPayrollForm
    permission_required = 'hr.manage_payroll' # Using the custom permission

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Start New Payroll Run"
        return context

    def get_success_url(self):
        # This method is called by form_valid. We can't set a dynamic URL
        # as a class attribute, so we override this method.
        # self.new_payroll_run is set in form_valid upon successful creation.
        if hasattr(self, 'new_payroll_run'):
            return self.new_payroll_run.get_absolute_url()
        return reverse_lazy('hr:payroll_run_list')

    def form_valid(self, form):
        """
        Main handler for when the payroll processing form is submitted and valid.
        This version uses the grade-based salary structure.
        """
        month = int(form.cleaned_data['month'])
        year = int(form.cleaned_data['year'])
        payment_date = form.cleaned_data['payment_date']
        
        pay_period_start = date(year, month, 1)
        _, end_day = monthrange(year, month)
        pay_period_end = date(year, month, end_day)

        # Check if a run for this period already exists
        if PayrollRun.objects.filter(pay_period_start=pay_period_start).exclude(status=PayrollRun.Status.CANCELLED).exists():
            messages.error(self.request, f"Payroll has already been processed or is in draft for {pay_period_start.strftime('%B %Y')}.")
            return self.form_invalid(form)

        try:
            with transaction.atomic():
                # 1. Fetch all active staff with their salary structures and grade rules prefetched
                staff_to_pay = StaffUser.objects.filter(is_active=True).select_related(
                    'salary_details__grade' # StaffUser -> StaffSalary -> SalaryGrade
                ).prefetch_related(
                    'salary_details__grade__rules__component' # StaffSalary -> SalaryGrade -> GradeRules -> SalaryComponent
                )

                if not staff_to_pay.exists():
                    messages.warning(self.request, "No active staff found to process payroll for.")
                    return super().form_valid(form)

                # 2. Create the parent PayrollRun object
                payroll_run = PayrollRun.objects.create(
                    pay_period_start=pay_period_start,
                    pay_period_end=pay_period_end,
                    payment_date=payment_date,
                    status=PayrollRun.Status.PROCESSED, # Mark as Processed immediately
                    processed_by=self.request.user,
                    processed_at=timezone.now(),
                )

                payslips_to_create = []
                staff_skipped_names = []
                
                # 3. Loop through staff and prepare payslip data
                for staff in staff_to_pay:
                    try:
                        # Get the salary structure (prefetched, so no DB hit)
                        salary_details = staff.salary_details
                        
                        if not salary_details.grade:
                            staff_skipped_names.append(f"{staff.get_full_name()} (no grade assigned)")
                            continue

                        basic_salary = salary_details.basic_salary
                        
                        # Initialize payslip data dictionaries from the grade's rules
                        allowances = Decimal('0.00')
                        bonuses = Decimal('0.00') # Placeholder for now
                        tax = Decimal('0.00')
                        pension = Decimal('0.00')
                        loans = Decimal('0.00')
                        other_deductions = Decimal('0.00')

                        for rule in salary_details.grade.rules.all():
                            component = rule.component
                            calculated_value = rule.calculate_amount(basic_salary)

                            if component.type == SalaryComponent.ComponentType.EARNING:
                                # This could be expanded to check component.name for more specific earnings
                                allowances += calculated_value
                            else: # DEDUCTION
                                # Here you would have logic to map components to specific deduction fields
                                # For now, we'll use a simple example mapping
                                if 'tax' in component.name.lower():
                                    tax += calculated_value
                                elif 'pension' in component.name.lower():
                                    pension += calculated_value
                                else:
                                    other_deductions += calculated_value
                        
                        # Add a Payslip object to a list for bulk creation
                        payslips_to_create.append(
                            Payslip(
                                payroll_run=payroll_run,
                                staff_member=staff,
                                basic_salary=basic_salary,
                                allowances=allowances,
                                bonuses=bonuses,
                                tax_deductions=tax,
                                pension_deductions=pension,
                                loan_repayments=loans,
                                other_deductions=other_deductions
                                # The save() method of Payslip will calculate the totals
                            )
                        )

                    except StaffSalary.DoesNotExist:
                        staff_skipped_names.append(f"{staff.get_full_name()} (no salary record)")
                    except Exception as e:
                        logger.error(f"Could not prepare payslip data for {staff.get_full_name()}: {e}", exc_info=True)
                        messages.error(self.request, f"Could not generate payslip for {staff.get_full_name()} due to a calculation error.")

                # 4. Bulk create all payslips. Note: This bypasses individual .save() methods.
                if payslips_to_create:
                    # We must manually call calculate_totals for each object before bulk_create
                    for p in payslips_to_create:
                        p.calculate_totals()
                    Payslip.objects.bulk_create(payslips_to_create)

                # 5. Finalize the Payroll Run record notes
                notes = f"Generated {len(payslips_to_create)} payslips."
                if staff_skipped_names:
                    notes += f" Skipped {len(staff_skipped_names)} staff: {', '.join(staff_skipped_names)}."
                
                payroll_run.notes = notes
                payroll_run.save(update_fields=['notes'])

                messages.success(self.request, f"Successfully processed payroll and generated {len(payslips_to_create)} payslips.")
                self.new_payroll_run = payroll_run # Store for get_success_url

        except Exception as e:
            logger.error(f"A critical error occurred during the payroll processing transaction: {e}", exc_info=True)
            messages.error(self.request, "An unexpected system error occurred. The payroll run was not completed.")
            # On error, success_url remains the default (payroll list)

        return super().form_valid(form)
    
    


# class ProcessPayrollView(LoginRequiredMixin, FormView):
#     template_name = 'hr/process_payroll_form.html'
#     form_class = ProcessPayrollForm
#     # The success_url will be dynamically set to the new run's detail page
#     # but we need a fallback.
#     success_url = reverse_lazy('hr:payroll_run_list')
#     # permission_required = 'hr.add_payrollrun' # Good to have for security

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['view_title'] = "Start New Payroll Run"
#         return context

#     def form_valid(self, form):
#         """
#         Main handler for when the payroll processing form is submitted and valid.
#         """
#         month = int(form.cleaned_data['month'])
#         year = int(form.cleaned_data['year'])
#         payment_date = form.cleaned_data['payment_date']
        
#         pay_period_start = date(year, month, 1)
#         _, end_day = monthrange(year, month)
#         pay_period_end = date(year, month, end_day)

#         # Check if a run for this period already exists
#         if PayrollRun.objects.filter(pay_period_start=pay_period_start).exists():
#             messages.error(self.request, f"Payroll has already been run for {pay_period_start.strftime('%B %Y')}.")
#             return self.form_invalid(form)

#         try:
#             with transaction.atomic():
#                 # 1. Fetch all active staff with their salary structures and components prefetched
#                 staff_to_pay = StaffUser.objects.filter(is_active=True).select_related(
#                     'salary_structure'
#                 ).prefetch_related(
#                     'salary_structure__components__component'
#                 )

#                 if not staff_to_pay.exists():
#                     messages.warning(self.request, "No active staff found to process payroll for.")
#                     return super().form_valid(form)

#                 # 2. Create the parent PayrollRun object in DRAFT status first
#                 payroll_run = PayrollRun.objects.create(
#                     pay_period_start=pay_period_start, # Corrected field name
#                     pay_period_end=pay_period_end,     # Corrected field name
#                     payment_date=payment_date,
#                     status=PayrollRun.Status.DRAFT,  # Start as Draft
#                     processed_by=self.request.user,
#                 )

#                 payslips_to_create = []
#                 staff_skipped_names = []
                
#                 # 3. Loop through staff and prepare payslip data
#                 for staff in staff_to_pay:
#                     try:
#                         # --- NEW GRADE-BASED LOGIC ---
#                         salary_details = staff.salary_details
                        
#                         if not salary_details.grade:
#                             logger.warning(f"Skipping payroll for {staff.get_full_name()}: No salary grade assigned.")
#                             staff_skipped_names.append(f"{staff.get_full_name()} (no grade)")
#                             continue

#                         basic_salary = salary_details.basic_salary
                        
#                         earnings = {'basic_salary': basic_salary}
#                         deductions = {}

#                         # Calculate amounts from the grade's rules
#                         for rule in salary_details.grade.rules.all(): # .all() is free due to prefetch
#                             component_value = rule.calculate_amount(basic_salary) # Assuming GradeRule has this method
#                             if rule.component.type == 'EARNING':
#                                 earnings[rule.component.name] = component_value
#                             else: # DEDUCTION
#                                 deductions[rule.component.name] = component_value
                        
#                         # --- Map to Payslip model fields ---
#                         payslip_data = {
#                             'basic_salary': earnings.pop('basic_salary', Decimal('0.00')),
#                             'allowances': sum(earnings.values()),
#                             'tax_deductions': deductions.get('PAYE Tax', Decimal('0.00')),
#                             'pension_deductions': deductions.get('Pension Contribution', Decimal('0.00')),
#                         }

#                         Payslip.objects.create(
#                             payroll_run=payroll_run,
#                             staff_member=staff,
#                             **payslip_data
#                         )
#                         payslips_created_count += 1

#                     except StaffSalary.DoesNotExist:
#                         staff_skipped_names.append(f"{staff.get_full_name()} (no salary record)")
#                         logger.warning(f"Skipping payroll for {staff.get_full_name()}: No salary record found.")
                        
#                 # for staff in staff_to_pay:
#                 #     try:
#                 #         # Get the salary structure (prefetched, so no DB hit)
#                 #         structure = staff.salary_structure
#                 #         basic_salary = structure.basic_salary

#                 #         # Separate earnings (allowances) and deductions from components
#                 #         allowances = Decimal('0.00')
#                 #         deductions = Decimal('0.00')
                        
#                 #         for item in structure.components.all():
#                 #             component_value = item.calculate_amount(basic_salary)
#                 #             if item.component.type == 'EARNING':
#                 #                 allowances += component_value
#                 #             else: # DEDUCTION
#                 #                 deductions += component_value
                        
#                 #         # --- Here you would use a more advanced calculation service if needed ---
#                 #         # For now, we will map directly. This assumes your salary components
#                 #         # contain items for tax, pension etc.
#                 #         # service = PayrollCalculationService(basic_salary, allowances, other_deductions)
#                 #         # calculated_data = service.calculate()
                        
#                 #         # Add a Payslip object to a list for bulk creation.
#                 #         # This maps directly to your Payslip model fields.
#                 #         payslips_to_create.append(
#                 #             Payslip(
#                 #                 payroll_run=payroll_run,
#                 #                 staff_member=staff,
#                 #                 basic_salary=basic_salary,
#                 #                 allowances=allowances,
#                 #                 bonuses=Decimal('0.00'), # Add logic for bonuses if needed
#                 #                 # This assumes your deductions are simple for now.
#                 #                 # A real system would have specific tax/pension components.
#                 #                 tax_deductions=deductions, # Placeholder: putting all deductions into tax
#                 #                 pension_deductions=Decimal('0.00'),
#                 #                 loan_repayments=Decimal('0.00'),
#                 #                 other_deductions=Decimal('0.00'),
#                 #             )
#                 #         )

#                 #     except StaffSalary.DoesNotExist:
#                 #         staff_skipped_names.append(staff.get_full_name() or staff.email)
#                 #         logger.warning(f"Skipping payroll for {staff.get_full_name()}: No salary structure.")
#                 #     except Exception as e:
#                 #         logger.error(f"Could not prepare payslip data for {staff.get_full_name()}: {e}", exc_info=True)
#                 #         messages.error(self.request, f"Could not generate payslip for {staff.get_full_name()} due to a calculation error.")

#                 # 4. Bulk create all payslips if any were generated
#                 if payslips_to_create:
#                     # The save() method of each payslip will be called, calculating totals.
#                     Payslip.objects.bulk_create(payslips_to_create)

#                 # 5. Finalize the Payroll Run record
#                 notes = f"Generated {len(payslips_to_create)} payslips."
#                 if staff_skipped_names:
#                     notes += f" Skipped {len(staff_skipped_names)} staff: {', '.join(staff_skipped_names)}."
                
#                 payroll_run.notes = notes
#                 payroll_run.status = PayrollRun.Status.PROCESSED # Mark as processed
#                 payroll_run.processed_at = timezone.now()
#                 payroll_run.save()

#                 messages.success(self.request, f"Successfully processed payroll and generated {len(payslips_to_create)} payslips.")
#                 self.success_url = payroll_run.get_absolute_url()

#         except Exception as e:
#             logger.error(f"A critical error occurred during the payroll processing transaction: {e}", exc_info=True)
#             messages.error(self.request, "An unexpected system error occurred. The payroll run was not completed.")
#             # On critical failure, success_url remains the default (payroll list)

#         return super().form_valid(form)

#     # def form_valid(self, form):
#     #     """
#     #     Main handler for when the payroll processing form is submitted and valid.
#     #     This version integrates a dedicated service for tax and statutory calculations.
#     #     """
#     #     month = int(form.cleaned_data['month'])
#     #     year = int(form.cleaned_data['year'])
#     #     payment_date = form.cleaned_data['payment_date']
        
#     #     pay_period_start = date(year, month, 1)
#     #     _, end_day = monthrange(year, month)
#     #     pay_period_end = date(year, month, end_day)

#     #     if PayrollRun.objects.filter(start_date=pay_period_start).exists():
#     #         messages.error(self.request, f"Payroll has already been run for {pay_period_start.strftime('%B %Y')}. Cannot create duplicates.")
#     #         return self.form_invalid(form)

#     #     try:
#     #         with transaction.atomic():
#     #             # --- 1. Fetch Staff to Pay (Your existing query is excellent) ---
#     #             staff_to_pay = StaffUser.objects.filter(is_active=True).select_related(
#     #                 'salary_structure'
#     #             ).prefetch_related(
#     #                 'salary_structure__components__component'
#     #             )

#     #             if not staff_to_pay.exists():
#     #                 messages.warning(self.request, "No active staff found to process payroll for.")
#     #                 # Still a "valid" form submission, just nothing to do.
#     #                 return super().form_valid(form)

#     #             # --- 2. Create the parent PayrollRun object ---
#     #             payroll_run = PayrollRun.objects.create(
#     #                 start_date=pay_period_start,
#     #                 end_date=pay_period_end,
#     #                 payment_date=payment_date,
#     #                 status=PayrollRun.Status.PROCESSED,
#     #                 processed_by=self.request.user,
#     #                 processed_at=timezone.now(),
#     #             )

#     #             payslips_to_create = []
#     #             staff_skipped_names = []
                
#     #             # --- 3. Loop through staff and generate payslip data ---
#     #             for staff in staff_to_pay:
#     #                 try:
#     #                     structure = staff.salary_structure
                        
#     #                     # --- CONSOLIDATED LOGIC: Calculate Gross from components ---
#     #                     gross_salary = Decimal('0.00')
#     #                     one_off_adjustments = Decimal('0.00') # For non-statutory deductions/bonuses

#     #                     for item in structure.components.all(): # .all() is free due to prefetch
#     #                         component = item.component
#     #                         if component.component_type == 'EARNING':
#     #                             gross_salary += item.amount
#     #                         elif component.component_type == 'DEDUCTION':
#     #                             # These are treated as one-off adjustments now
#     #                             one_off_adjustments -= item.amount

#     #                     # --- NEW: Use the Calculation Service ---
#     #                     service = PayrollCalculationService(
#     #                         monthly_gross_salary=gross_salary,
#     #                         one_off_adjustments=one_off_adjustments
#     #                     )
#     #                     calculated_data = service.calculate()

#     #                     # Add a Payslip object to a list for bulk creation
#     #                     # This uses the new fields from the Payslip model.
#     #                     payslips_to_create.append(
#     #                         Payslip(
#     #                             payroll_run=payroll_run,
#     #                             staff=staff,
#     #                             gross_salary=calculated_data['gross_salary'],
#     #                             tax_deducted=calculated_data['tax_deducted'],
#     #                             other_deductions=calculated_data['other_deductions'], # This is a JSON object
#     #                             adjustments=calculated_data['adjustments'],
#     #                             total_deductions=calculated_data['total_deductions'],
#     #                             net_pay=calculated_data['net_pay'],
#     #                         )
#     #                     )

#     #                 except StaffSalaryStructure.DoesNotExist:
#     #                     staff_skipped_names.append(staff.get_full_name() or staff.email)
#     #                     logger.warning(f"Skipping payroll for {staff.get_full_name()} as they do not have a salary structure.")
#     #                 except Exception as e:
#     #                     logger.error(f"Could not calculate payslip data for {staff.get_full_name()}: {e}", exc_info=True)
#     #                     messages.warning(self.request, f"Could not generate payslip for {staff.get_full_name()} due to a calculation error.")

#     #             # --- 4. Bulk create all payslips ---
#     #             if payslips_to_create:
#     #                 Payslip.objects.bulk_create(payslips_to_create)

#     #             # --- 5. Finalize the Payroll Run record ---
#     #             notes = f"Generated {len(payslips_to_create)} payslips."
#     #             if staff_skipped_names:
#     #                 notes += f" Skipped {len(staff_skipped_names)} staff due to missing salary structure: {', '.join(staff_skipped_names)}."
#     #             payroll_run.notes = notes
#     #             payroll_run.save()

#     #             messages.success(self.request, f"Successfully processed payroll and generated {len(payslips_to_create)} payslips for {payroll_run.name()}.")
                
#     #             # Set the success URL to the detail page of the run we just created
#     #             # Assumes your PayrollRun model has a get_absolute_url method
#     #             self.success_url = reverse_lazy('hr:payroll_run_detail', kwargs={'pk': payroll_run.pk})

#     #     except Exception as e:
#     #         logger.error(f"A critical error occurred during payroll processing: {e}", exc_info=True)
#     #         messages.error(self.request, "An unexpected system error occurred. The payroll run was not completed. Please check server logs for details.")

#     #     return super().form_valid(form)


    
from django.views import View
from django.contrib.auth.mixins import AccessMixin

class MarkPayrollPaidView(TenantPermissionRequiredMixin, AccessMixin, View):
    permission_required = 'hr.manage_payroll'

    def post(self, request, *args, **kwargs):
        payroll_run = get_object_or_404(PayrollRun, pk=self.kwargs.get('pk'))
        
        if payroll_run.status == PayrollRun.Status.PROCESSED:
            payroll_run.status = PayrollRun.Status.PAID
            # You could also set the paid_on date for all related payslips here
            # payroll_run.payslips.all().update(paid_on=timezone.now().date(), status='PAID')
            payroll_run.save()
            messages.success(request, f"Payroll run for {payroll_run} has been marked as PAID.")
        else:
            messages.warning(request, f"This payroll run is not in a 'Processed' state and cannot be marked as paid.")
            
        return redirect(payroll_run.get_absolute_url())
    




# D:\school_fees_saas_v2\apps\hr\views.py

from django.views.generic.edit import UpdateView
from django.db import transaction
from apps.schools.models import StaffUser # Import StaffUser
from .models import StaffSalaryStructure
from .forms import StaffSalaryStructureForm, SalaryStructureComponentFormSet

class StaffSalaryStructureUpdateView(TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = StaffSalaryStructure
    form_class = StaffSalaryStructureForm
    template_name = 'hr/staff_salary_structure_form.html'
    permission_required = 'hr.change_staffsalarystructure' # Assuming default permission
    success_message = "Salary structure for %(staff_user)s updated successfully."

    def get_object(self, queryset=None):
        """
        Gets or creates a Salary Structure for the staff member specified in the URL.
        """
        self.staff_user = get_object_or_404(StaffUser, pk=self.kwargs['staff_pk'])
        
        # get_or_create returns a tuple (object, created_boolean)
        structure, created = StaffSalaryStructure.objects.get_or_create(
            staff_user=self.staff_user,
            defaults={'effective_date': timezone.now().date()}
        )
        if created:
            logger.info(f"Created new blank salary structure for {self.staff_user.get_full_name()}")
        
        return structure

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Manage Salary for: {self.staff_user.get_full_name()}"
        context['staff_member'] = self.staff_user
        
        if self.request.POST:
            context['component_formset'] = SalaryStructureComponentFormSet(self.request.POST, instance=self.object)
        else:
            context['component_formset'] = SalaryStructureComponentFormSet(instance=self.object)
            
        return context

    def post(self, request, *args, **kwargs):
        """
        Handle POST requests, binding the main form and the formset.
        """
        self.object = self.get_object()
        form = self.get_form()
        formset = SalaryStructureComponentFormSet(request.POST, instance=self.object)

        if form.is_valid() and formset.is_valid():
            return self.form_valid(form, formset)
        else:
            return self.form_invalid(form, formset)

    def form_valid(self, form, formset):
        """
        Called when both the main form and the formset are valid.
        """
        try:
            with transaction.atomic():
                self.object = form.save()
                formset.instance = self.object
                formset.save()
        except Exception as e:
            logger.error(f"Error saving salary structure for {self.staff_user.email}: {e}", exc_info=True)
            messages.error(self.request, "An unexpected error occurred while saving.")
            return self.form_invalid(form, formset)

        messages.success(self.request, self.get_success_message(form.cleaned_data))
        return redirect(self.get_success_url())

    def form_invalid(self, form, formset):
        """
        Called when either the form or the formset is invalid.
        """
        # We need to pass the invalid formset back to the context
        context = self.get_context_data(form=form, component_formset=formset)
        return self.render_to_response(context)

    def get_success_url(self):
        # Redirect back to the staff member's detail page
        return reverse('schools:staff_detail', kwargs={'pk': self.staff_user.pk})




# apps/hr/views.py
from django.db import transaction
from django.urls import reverse_lazy
from django.views.generic import CreateView, UpdateView, ListView
from .models import SalaryGrade
from .forms import SalaryGradeForm, SalaryGradeComponentFormSet

# You'll need a list view to navigate from
class SalaryGradeListView(ListView):
    model = SalaryGrade
    template_name = 'hr/salarygrade_list.html' # You will need to create this list template
    context_object_name = 'grades'

# --- VIEW FOR CREATING A NEW SALARY GRADE ---
class SalaryGradeCreateView(CreateView):
    model = SalaryGrade
    form_class = SalaryGradeForm
    template_name = 'hr/salarygrade_form.html'
    success_url = reverse_lazy('hr:salarygrade_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Create New Salary Grade"
        if self.request.POST:
            context['components_formset'] = SalaryGradeComponentFormSet(self.request.POST, prefix='components')
        else:
            context['components_formset'] = SalaryGradeComponentFormSet(prefix='components')
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['components_formset']
        
        with transaction.atomic():
            self.object = form.save()
            if formset.is_valid():
                formset.instance = self.object
                formset.save()
            else:
                # If formset is not valid, re-render the page with errors
                return self.form_invalid(form)

        return super().form_valid(form)

# --- VIEW FOR UPDATING AN EXISTING SALARY GRADE ---
class SalaryGradeUpdateView(UpdateView):
    model = SalaryGrade
    form_class = SalaryGradeForm
    template_name = 'hr/salarygrade_form.html'
    success_url = reverse_lazy('hr:salarygrade_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit Salary Grade: {self.object.name}"
        if self.request.POST:
            context['components_formset'] = SalaryGradeComponentFormSet(self.request.POST, instance=self.object, prefix='components')
        else:
            context['components_formset'] = SalaryGradeComponentFormSet(instance=self.object, prefix='components')
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['components_formset']
        
        with transaction.atomic():
            self.object = form.save()
            if formset.is_valid():
                formset.instance = self.object
                formset.save()
            else:
                return self.form_invalid(form)

        return super().form_valid(form)


from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView
from .models import Payslip

class StaffPayslipListView(LoginRequiredMixin, ListView):
    model = Payslip
    template_name = 'hr/staff_payslip_list.html'
    context_object_name = 'payslip_list'

    def get_queryset(self):
        """
        This is the most important part! It overrides the default queryset
        to only return payslips belonging to the currently logged-in user.
        """
        # Order by the most recent payroll run first
        return Payslip.objects.filter(staff_member=self.request.user).order_by('-payroll_run__pay_period_start')
    
    
    
    
# apps/hr/views.py

# Make sure you have these imports
from django.db.models import Sum
from django.views.generic import ListView
from .models import PayrollRun, Payslip
from django.db.models import Count, Sum

class PayrollSummaryReportView(ListView):
    model = PayrollRun
    template_name = 'hr/payroll_summary_report.html'
    context_object_name = 'payroll_runs'
    # permission_required = 'hr.view_payrollrun' # Protect this view

    def get_queryset(self):
        """
        Only show payroll runs that are 'Processed' or 'Paid'
        """
        return PayrollRun.objects.filter(
            status__in=[PayrollRun.Status.PROCESSED, PayrollRun.Status.PAID]
        ).order_by('-pay_period_start')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "Payroll Summary Report"

        # Check if a specific payroll run has been selected from the list
        selected_run_pk = self.request.GET.get('run_pk')
        selected_run = None
        summary_data = None

        if selected_run_pk:
            try:
                # Get the selected payroll run object
                selected_run = PayrollRun.objects.get(pk=selected_run_pk)

                # Use Django's aggregation power to calculate totals
                # This is extremely efficient - it's one database query!
                summary_data = Payslip.objects.filter(payroll_run=selected_run).aggregate(
                    total_gross=Sum('gross_earnings'),      # Corrected
                    total_allowances=Sum('allowances'),
                    total_deductions=Sum('total_deductions'), # Corrected
                    total_net=Sum('net_pay'),
                    payslip_count=Count('id')
                )

            except (PayrollRun.DoesNotExist, ValueError):
                # Handle cases where the run_pk is invalid
                messages.error(self.request, "The selected payroll run could not be found.")

        context['selected_run'] = selected_run
        context['summary_data'] = summary_data

        return context
    
    
    
    
# apps/hr/views.py

from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from .models import StatutoryDeduction
# You may need to create a form if you want custom widgets, but for now, we'll let the views handle it.

# ... your other views ...

# --- CRUD Views for StatutoryDeduction ---

class StatutoryDeductionListView(LoginRequiredMixin, PermissionRequiredMixin, ListView):
    model = StatutoryDeduction
    template_name = 'hr/statutorydeduction_list.html'
    context_object_name = 'deductions'
    permission_required = 'hr.view_statutorydeduction'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = 'Manage Statutory Deductions'
        return context

class StatutoryDeductionCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = StatutoryDeduction
    template_name = 'hr/statutorydeduction_form.html'
    fields = ['name', 'payslip_label', 'employee_contribution_rate', 'employer_contribution_rate', 'is_active']
    success_url = reverse_lazy('hr:deduction_list')
    permission_required = 'hr.add_statutorydeduction'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = 'Create New Statutory Deduction'
        return context

class StatutoryDeductionUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = StatutoryDeduction
    template_name = 'hr/statutorydeduction_form.html'
    fields = ['name', 'employee_contribution_rate', 'employer_contribution_rate', 'is_active']
    success_url = reverse_lazy('hr:deduction_list')
    permission_required = 'hr.change_statutorydeduction'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Edit: {self.object.name}"
        return context

class StatutoryDeductionDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    model = StatutoryDeduction
    template_name = 'hr/statutorydeduction_confirm_delete.html'
    success_url = reverse_lazy('hr:deduction_list')
    permission_required = 'hr.delete_statutorydeduction'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Delete: {self.object.name}"
        return context
    
    
    


# D:\school_fees_saas_v2\apps/hr/views.py

from django.views import View
from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse
from django.contrib import messages
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

from .models import LeaveRequest, LeaveBalance, EmployeeProfile
from apps.common.mixins import TenantPermissionRequiredMixin

from django.views.generic.edit import UpdateView
from django.utils import timezone
from .models import LeaveRequest, LeaveRequestStatusChoices # <<< IMPORT THE CHOICES CLASS
from .forms import AdminLeaveRequestUpdateForm

class AdminLeaveRequestUpdateView(TenantPermissionRequiredMixin, SuccessMessageMixin, UpdateView):
    model = LeaveRequest
    form_class = AdminLeaveRequestUpdateForm
    template_name = 'hr/admin_leaverequest_form.html'
    permission_required = 'hr.change_leaverequest'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = f"Review Leave Request: {self.object.employee.user.get_full_name()}"
        return context

    def get_queryset(self):
        # Ensure admins can only act on PENDING requests through this view
        return super().get_queryset().filter(status=LeaveRequestStatusChoices.PENDING)
    
    
    def form_valid(self, form):
        leave_request = self.get_object()
        new_status = form.cleaned_data['status']
        
        # Check if the user has enough leave days available BEFORE approving
        if new_status == LeaveRequestStatusChoices.APPROVED:
            try:
                # Find the corresponding leave balance
                balance = LeaveBalance.objects.get(
                    employee=leave_request.employee,
                    leave_type=leave_request.leave_type,
                    year=str(leave_request.start_date.year)
                )
                # The duration is already calculated and stored on the request
                if leave_request.duration > balance.days_available:
                    messages.error(self.request, f"Cannot approve. Staff member has only {balance.days_remaining} days available for this leave type, but requested {leave_request.duration}.")
                    # Re-render the form with the error
                    return self.form_invalid(form)
            except LeaveBalance.DoesNotExist:
                messages.error(self.request, f"Cannot approve. No leave balance record found for this leave type for the year {leave_request.start_date.year}.")
                return self.form_invalid(form)

        # If all checks pass, or if it's a rejection, proceed to update the request
        leave_request.status = new_status
        leave_request.status_reason = form.cleaned_data.get('status_reason', '')
        leave_request.approved_by = self.request.user.hr_profile
        leave_request.status_changed_at = timezone.now()
        leave_request.save()

        # Set success message
        if leave_request.status == LeaveRequestStatusChoices.APPROVED:
            messages.success(self.request, "Leave request has been approved.")
        else:
            messages.warning(self.request, "Leave request has been rejected.")
            
        return redirect(self.get_success_url())

    # def form_valid(self, form):
    #     """
    #     This method is called when the form is valid. We add the logic here
    #     to update the status and log who approved/rejected it.
    #     """
    #     leave_request = self.get_object()
    #     new_status = form.cleaned_data['status']
        
    #     # --- THIS IS THE FIX ---
    #     # Compare against the correct choices class
    #     if new_status == LeaveRequestStatusChoices.APPROVED:
    #         # Logic for approval
    #         leave_request.status = LeaveRequestStatusChoices.APPROVED
    #         messages.success(self.request, f"Leave request for {leave_request.employee.user.get_full_name()} has been APPROVED.")
    #     elif new_status == LeaveRequestStatusChoices.REJECTED:
    #         # Logic for rejection
    #         leave_request.status = LeaveRequestStatusChoices.REJECTED
    #         messages.warning(self.request, f"Leave request for {leave_request.employee.user.get_full_name()} has been REJECTED.")
        
    #     # Log the admin who took the action and when
    #     leave_request.approved_by = self.request.user.hr_profile # Assumes staff has hr_profile
    #     leave_request.status_changed_at = timezone.now()
        
    #     # The form already saves status_reason, so we just need to save the other fields
    #     leave_request.save()
        
    #     # The success message will be displayed on the next page
    #     self.success_message = f"Leave request status updated to '{leave_request.get_status_display()}'."

    #     # Since the main form.save() is what UpdateView's super().form_valid() does,
    #     # and we've manually updated the object, we can let it proceed.
    #     # But it's cleaner to handle the save fully and then redirect.
    #     response = super().form_valid(form)
    #     return response

    def get_success_url(self):
        # Redirect back to the list of requests after taking action
        return reverse_lazy('hr:admin_leaverequest_list')
    
    

# # ... your other HR views ...

# class AdminLeaveRequestUpdateView(TenantPermissionRequiredMixin, View):
#     """
#     A view for administrators to review, approve, or reject a leave request.
#     """
#     template_name = 'hr/admin_leaverequest_update_form.html'
#     permission_required = 'hr.change_leaverequest' # Permission to approve/reject

#     def get(self, request, *args, **kwargs):
#         leave_request = get_object_or_404(LeaveRequest, pk=self.kwargs['pk'])
#         context = {
#             'view_title': f"Review Leave Request: {leave_request.employee.get_full_name()}",
#             'leave_request': leave_request
#         }
#         return render(request, self.template_name, context)

#     def post(self, request, *args, **kwargs):
#         leave_request = get_object_or_404(LeaveRequest, pk=self.kwargs['pk'])
        
#         # Determine which button was clicked ('approve' or 'reject')
#         action = request.POST.get('action')

#         if leave_request.status != LeaveRequest.Status.PENDING:
#             messages.warning(request, "This leave request has already been actioned.")
#             return redirect(leave_request.get_admin_detail_url()) # Assuming a detail URL exists

#         if action == 'approve':
#             return self.handle_approval(request, leave_request)
#         elif action == 'reject':
#             return self.handle_rejection(request, leave_request)
#         else:
#             messages.error(request, "Invalid action specified.")
#             return redirect(leave_request.get_admin_detail_url())

#     def handle_approval(self, request, leave_request):
#         # --- This is the LEAVE BALANCE DEDUCTION LOGIC ---
#         try:
#             with transaction.atomic():
#                 # 1. Update the leave request status
#                 leave_request.status = LeaveRequest.Status.APPROVED
#                 leave_request.approved_by = request.user
#                 leave_request.save()

#                 # 2. Update the 'days_taken' on the correct LeaveBalance record
#                 leave_year = str(leave_request.start_date.year)
                
#                 balance, created = LeaveBalance.objects.get_or_create(
#                     employee=leave_request.employee,
#                     leave_type=leave_request.leave_type,
#                     year_or_period_info=leave_year
#                 )
                
#                 # Calculate duration in days
#                 duration_in_days = (leave_request.end_date - leave_request.start_date).days + 1
                
#                 balance.days_taken += Decimal(duration_in_days)
#                 balance.save()
                
#                 logger.info(
#                     f"Added {duration_in_days} days to 'days_taken' for '{balance.leave_type.name}' for "
#                     f"'{balance.employee.get_full_name()}'. New total taken: {balance.days_taken}"
#                 )
#                 messages.success(request, "Leave request has been APPROVED and the balance updated.")

#         except EmployeeProfile.DoesNotExist:
#             messages.error(request, "Could not find an employee profile for this staff member. Action failed.")
#         except Exception as e:
#             messages.error(request, "An error occurred while updating the leave balance. The request status was not changed.")
#             logger.error(f"Error approving leave request {leave_request.pk}: {e}", exc_info=True)
            
#         return redirect('hr:admin_leaverequest_list') # Redirect to the list of requests

#     def handle_rejection(self, request, leave_request):
#         leave_request.status = LeaveRequest.Status.REJECTED
#         leave_request.approved_by = request.user # Store who actioned it
#         leave_request.save()
#         messages.info(request, "Leave request has been REJECTED.")
#         return redirect('hr:admin_leaverequest_list')
    


# D:\school_fees_saas_v2\apps\hr\views.py
from django.views.generic import TemplateView

class StaffDashboardView(StaffLoginRequiredMixin, TemplateView):
    template_name = "hr/staff_dashboard.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "My HR Dashboard"
        # You can add context here like a list of the user's recent leave requests, etc.
        # context['recent_requests'] = LeaveRequest.objects.filter(employee__staff_user=self.request.user)[:5]
        return context



class HRDashboardView(TenantPermissionRequiredMixin, TemplateView):
    template_name = 'hr/hr_dashboard.html'
    permission_required = 'hr.view_hr_module' # Use your high-level module permission

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['view_title'] = "HR & Staff Dashboard"
        # You can add summary stats here later, like total staff, pending leave requests, etc.
        # context['pending_leave_count'] = LeaveRequest.objects.filter(status='PENDING').count()
        return context