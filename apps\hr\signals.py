# D:\school_fees_saas_v2\apps\hr\signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
import logging

from apps.schools.models import StaffUser
from .models import EmployeeProfile

logger = logging.getLogger(__name__)

@receiver(post_save, sender=StaffUser)
def create_employee_profile_on_staff_creation(sender, instance, created, **kwargs):
    """
    Signal to automatically create an EmployeeProfile when a new StaffUser is created.
    """
    if created:
        try:
            # Use get_or_create to be safe in case something else already created it
            profile, profile_created = EmployeeProfile.objects.get_or_create(user=instance)
            if profile_created:
                logger.info(f"Automatically created EmployeeProfile for new StaffUser: {instance.email}")
        except Exception as e:
            logger.error(f"Failed to auto-create EmployeeProfile for {instance.email}: {e}")




# # D:\school_fees_saas_v2\apps\hr\signals.py
# from django.db.models.signals import post_save
# from django.dispatch import receiver
# import logging

# # Import the models involved
# from apps.schools.models import StaffUser
# from .models import EmployeeProfile

# logger = logging.getLogger(__name__)

# @receiver(post_save, sender=StaffUser)
# def create_or_update_employee_profile(sender, instance, created, **kwargs):
#     """
#     Signal to automatically create an EmployeeProfile when a new StaffUser is created.
#     """
#     # 'created' is a boolean that is True only when the record is first saved.
#     if created:
#         try:
#             # Check if a profile already exists just in case
#             if not hasattr(instance, 'employeeprofile'):
#                 EmployeeProfile.objects.create(user=instance)
#                 logger.info(f"Automatically created EmployeeProfile for new StaffUser: {instance.email}")
#         except Exception as e:
#             logger.error(f"Failed to auto-create EmployeeProfile for {instance.email}: {e}")


