# D:\school_fees_saas_v2\apps\hr\utils.py
import logging
from .models import EmployeeProfile
from apps.schools.models import StaffUser

logger = logging.getLogger(__name__)

def get_employee_profile_for_staff(staff_user: StaffUser):
    """
    Safely retrieves the EmployeeProfile for a given StaffUser.
    """
    if not isinstance(staff_user, StaffUser):
        logger.warning(f"get_employee_profile_for_staff called with non-staff user: {staff_user}")
        return None
    
    try:
        # --- THIS IS THE FIX ---
        # We confirmed from the `dir()` output that the attribute is 'hr_profile'.
        # Using this reverse accessor is the standard Django way.
        profile = staff_user.hr_profile
        logger.info(f"SUCCESS: Found EmployeeProfile via 'hr_profile' for {staff_user.email}")
        return profile
    except EmployeeProfile.DoesNotExist:
        # This error is raised if the relationship exists but the profile object itself was deleted.
        logger.warning(f"No EmployeeProfile found for StaffUser: {staff_user.email}. The relationship link is broken or the profile was deleted.")
        return None
    except AttributeError:
        # This is a fallback in case the related_name is something else entirely.
        # This shouldn't be hit now that we know the name is 'hr_profile'.
        logger.error(f"FATAL: The related_name for EmployeeProfile on StaffUser is not 'hr_profile'. Please check the model definition.")
        return None
    
    
    