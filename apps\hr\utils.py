# D:\school_fees_saas_v2\apps\hr\utils.py
import logging
from .models import EmployeeProfile
from apps.schools.models import StaffUser

logger = logging.getLogger(__name__)

def get_employee_profile_for_staff(staff_user: StaffUser):
    """
    Safely retrieves the EmployeeProfile for a given StaffUser.
    """
    # Enhanced debugging to understand the user object
    logger.info(f"get_employee_profile_for_staff called with user: {staff_user} (type: {type(staff_user)})")
    logger.info(f"User class name: {staff_user.__class__.__name__}")
    logger.info(f"User model name: {getattr(staff_user._meta, 'model_name', 'N/A') if hasattr(staff_user, '_meta') else 'No _meta'}")
    logger.info(f"Is StaffUser instance: {isinstance(staff_user, StaffUser)}")

    # Check if it's a lazy object that wraps a StaffUser
    if hasattr(staff_user, '_wrapped'):
        logger.info(f"User is wrapped object. Wrapped type: {type(staff_user._wrapped)}")
        logger.info(f"Wrapped is StaffUser: {isinstance(staff_user._wrapped, StaffUser)}")

    # Instead of isinstance check, check the model name or class name
    if not (isinstance(staff_user, StaffUser) or
            (hasattr(staff_user, '__class__') and staff_user.__class__.__name__ == 'StaffUser') or
            (hasattr(staff_user, '_meta') and staff_user._meta.model_name == 'staffuser')):
        logger.warning(f"get_employee_profile_for_staff called with non-staff user: {staff_user} (type: {type(staff_user)})")
        return None

    try:
        # Use the 'hr_profile' related_name to access the EmployeeProfile
        profile = staff_user.hr_profile
        logger.info(f"SUCCESS: Found EmployeeProfile via 'hr_profile' for {staff_user.email}")
        return profile
    except EmployeeProfile.DoesNotExist:
        # This error is raised if the relationship exists but the profile object itself was deleted.
        logger.warning(f"No EmployeeProfile found for StaffUser: {staff_user.email}. The relationship link is broken or the profile was deleted.")
        return None
    except AttributeError as e:
        # This is a fallback in case the related_name is something else entirely.
        logger.error(f"AttributeError accessing hr_profile for {staff_user.email}: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error accessing EmployeeProfile for {staff_user.email}: {e}")
        return None
    
    
    