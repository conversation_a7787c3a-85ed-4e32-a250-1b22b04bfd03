# D:\school_fees_saas_v2\apps\accounting\utils.py

import logging
from decimal import Decimal
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError # For create_and_post_journal_entry
from .models import AccountType
# --- MODEL IMPORTS ---
# Import your accounting models if not already globally imported in this file
# from .models import JournalEntry, JournalEntryLine, ChartOfAccount, AccountType 

# Import models from other apps that are used here
from .models import JournalEntry, JournalLine, Account, AccountType
from apps.fees.models import Invoice, InvoiceDetail # <<< ENSURE InvoiceDetail IS IMPORTED
from apps.tenants.models import School # Assuming School is your tenant model
from apps.schools.models import SchoolProfile # Assuming this holds accounting config linked to tenant.profile

from .models import JournalEntry

# --- UTILITY FUNCTION IMPORTS ---
# Assuming these are defined elsewhere in this utils.py or another utils file
# from .utils_config import get_tenant_accounting_config 
# from .utils_journal_entry import create_and_post_journal_entry 
# For now, I'll assume get_tenant_accounting_config and create_and_post_journal_entry are in this same file or correctly imported.


logger = logging.getLogger(__name__)

# ==============================================================================
# Placeholder for get_tenant_accounting_config (if not defined elsewhere)
# ==============================================================================
def get_tenant_accounting_config(tenant_obj: School) -> SchoolProfile:
    """
    Retrieves the SchoolProfile instance which holds accounting configurations.
    Raises ValueError if critical configurations are missing.
    """
    logger.info(f"--- get_tenant_accounting_config: CALLED for tenant: {tenant_obj.name} (PK: {tenant_obj.pk}) ---")
    # Get SchoolProfile from current tenant schema instead of tenant_obj.schoolprofile
    try:
        profile = SchoolProfile.objects.first()
            logger.error(f"get_tenant_accounting_config: Tenant '{tenant_obj.name}' has no SchoolProfile in current schema.")
            raise ValueError(_("School accounting profile configuration is missing for this tenant."))
    except Exception as e:
        logger.error(f"get_tenant_accounting_config: Error fetching SchoolProfile: {e}")
        raise ValueError(_("School accounting profile configuration could not be retrieved."))
    logger.info(f"get_tenant_accounting_config: Successfully fetched tenant_instance.profile. Profile PK: {profile.pk}")

    # Log the fetched values for debugging
    logger.info(f"get_tenant_accounting_config: --- Values from fetched profile (PK: {profile.pk}) for tenant '{tenant_obj.name}' ---")
    required_coa_fields = {
        'default_accounts_receivable_coa': 'Default Accounts Receivable Account',
        'default_fee_income_coa': 'Default Fee Income Account',
        # 'default_discount_given_coa': 'Default Discount Given Account', # Discount is optional for JE if not applied
    }
    all_coa_fields_present = True
    for field_name, readable_name in required_coa_fields.items():
        val = getattr(profile, field_name, None)
        logger.info(f"  {field_name}: {val} (PK: {val.pk if val else 'Not Set'}, IsControl: {val.is_control_account if val else 'N/A'}, Active: {val.is_active if val else 'N/A'})")
        if not val:
            all_coa_fields_present = False
            logger.error(f"get_tenant_accounting_config: CRITICAL - '{readable_name}' ({field_name}) is not set on SchoolProfile PK {profile.pk}.")
    
    # Optional fields logging
    logger.info(f"  default_discount_given_coa: {getattr(profile, 'default_discount_given_coa', None)}")
    logger.info(f"  default_cash_coa: {getattr(profile, 'default_cash_coa', None)}") # Not used by invoice JE directly
    logger.info(f"  default_bank_coa: {getattr(profile, 'default_bank_coa', None)}") # Not used by invoice JE directly
    logger.info(f"get_tenant_accounting_config: --- End values from fetched profile ---")

    if not all_coa_fields_present:
        raise ValueError(_("One or more critical default Chart of Account links are missing in the School Profile. Please configure them."))
    
    logger.info(f"get_tenant_accounting_config: Config checks passed. Returning SchoolProfile instance for tenant {tenant_obj.name}.")
    return profile

# ==============================================================================
# Placeholder for create_and_post_journal_entry (if not defined elsewhere)
# ==============================================================================
def create_and_post_journal_entry(user, entry_date, narration, lines_data, 
                                    entry_classification, source_document_obj=None, tenant=None):
    """
    Generic utility to create a JournalEntry and its lines, ensuring it's balanced.
    If balanced, sets status to POSTED. Otherwise, raises ValidationError.
    `lines_data` is a list of dicts: [{'account': <COA_obj>, 'amount': Decimal, 'entry_type': 'DR'/'CR', 'description': 'optional'}, ...]
    """
    logger.info(f"JE Creator: Attempting for '{narration}', User: {getattr(user, 'email', user)}, Classification: {entry_classification}")
    
    # Ensure models are accessible
    from .models import JournalEntry, JournalLine, Account 

    total_debits = sum(line['amount'] for line in lines_data if line['entry_type'] == 'DR')
    total_credits = sum(line['amount'] for line in lines_data if line['entry_type'] == 'CR')

    if total_debits != total_credits:
        error_msg = _("Journal entry lines do not balance. Debits: %(debits)s, Credits: %(credits)s") % {
            'debits': total_debits, 'credits': total_credits
        }
        logger.error(f"JE Creator: Unbalanced - Debits: {total_debits}, Credits: {total_credits} for '{narration}'")
        raise ValidationError([error_msg]) # Pass as a list for Django's error handling

    if not lines_data: # Or if total_debits == 0 and total_credits == 0 but lines_data might imply something
        logger.warning(f"JE Creator: No lines provided or zero total for '{narration}'. Skipping JE creation.")
        return None # Or raise error if JE must have lines

    with transaction.atomic():
        je = JournalEntry.objects.create(
            date=entry_date,
            narration=narration[:255], # Ensure it fits max_length
            entry_type=entry_classification, # e.g., JournalEntry.EntryType.INVOICE
            status=JournalEntry.StatusChoices.POSTED, # Auto-post system generated JEs
            created_by=user,
            # source_document=source_document_obj, # If using GenericForeignKey
            # tenant=tenant, # If JournalEntry has direct tenant FK
        )
        # If using GenericForeignKey for source_document:
        if source_document_obj:
            from django.contrib.contenttypes.models import ContentType
            je.content_type = ContentType.objects.get_for_model(source_document_obj)
            je.object_id = source_document_obj.pk
            je.save()


        for line_data in lines_data:
            JournalLine.objects.create(
                journal_entry=je,
                account=line_data['account'],
                debit_amount=line_data['amount'] if line_data['entry_type'] == 'DR' else Decimal('0.00'),
                credit_amount=line_data['amount'] if line_data['entry_type'] == 'CR' else Decimal('0.00'),
                description=line_data.get('description', '')[:255]
            )
        logger.info(f"JE Creator: Successfully created and posted JE-{je.pk} for '{narration}'")
        return je
# ==============================================================================


def create_invoice_je(user, invoice_obj: Invoice, tenant_obj: School, je_date=None):
    function_name_for_log = "create_invoice_je"
    logger.info(f"{function_name_for_log}: UTIL: Creating Journal Entry for Invoice PK {invoice_obj.pk} (Number: {invoice_obj.invoice_number})")

    try:
        acc_config_profile = get_tenant_accounting_config(tenant_obj)
        ar_account = acc_config_profile.default_accounts_receivable_coa
        discount_account_from_profile = acc_config_profile.default_discount_given_coa

        logger.info(f"{function_name_for_log}: Fetched AR Account: {ar_account}")
        logger.info(f"{function_name_for_log}: Fetched Discount Account: {discount_account_from_profile}")

        if not ar_account:
            raise ValueError(_("Default Accounts Receivable account is not configured in School Profile."))

        lines_for_je = []

        # These should come from the invoice instance after recalculate_financials has run
        # Ensure these attribute names on invoice_obj are correct
        net_invoice_amount_due = invoice_obj.total_amount # This IS THE NET AMOUNT PAYABLE
        total_discounts_on_invoice = invoice_obj.discount_amount_calc # This is SUM OF CONCESSIONS

        logger.debug(
            f"{function_name_for_log}: Invoice PK {invoice_obj.pk} - "
            f"Invoice.total_amount (Net Receivable): {net_invoice_amount_due}, "
            f"Invoice.discount_total_amount (Total Discounts): {total_discounts_on_invoice}"
        )

        # Debit Accounts Receivable for the net amount due
        if net_invoice_amount_due > Decimal('0.00'):
            lines_for_je.append({
                'account': ar_account,
                'amount': net_invoice_amount_due,
                'entry_type': 'DR',
                'description': _("Accounts Receivable for Invoice #%(inv_num)s") % {'inv_num': invoice_obj.invoice_number}
            })
        elif net_invoice_amount_due < Decimal('0.00'): # Credit Note scenario
            lines_for_je.append({
                'account': ar_account,
                'amount': abs(net_invoice_amount_due), # Amount is positive, type is CR
                'entry_type': 'CR',
                'description': _("AR Credit for Credit Note #%(inv_num)s") % {'inv_num': invoice_obj.invoice_number}
            })


        # Debit Discount Given if any discounts were applied
        if total_discounts_on_invoice > Decimal('0.00'):
            if not discount_account_from_profile:
                raise ValueError(_("Discount applied on invoice but no Default Discount Given Account is configured."))
            lines_for_je.append({
                'account': discount_account_from_profile,
                'amount': total_discounts_on_invoice,
                'entry_type': 'DR', # Discount Given is debited
                'description': _("Discounts & Concessions on Invoice #%(inv_num)s") % {'inv_num': invoice_obj.invoice_number}
            })

        # Credit Income Account(s) from FEE_ITEM type invoice line items
        income_lines_details_qs = invoice_obj.details.filter(
            line_type=InvoiceDetail.LineTypeChoices.FEE_ITEM
        ).select_related('fee_head__income_account', 'fee_head__income_account__account_type')

        actual_income_credited_debug = Decimal('0.00') # For debug log

        if not income_lines_details_qs.exists() and \
            (net_invoice_amount_due > Decimal('0.00') or (net_invoice_amount_due < Decimal('0.00') and total_discounts_on_invoice == Decimal('0.00'))):
            # If net amount is positive, or it's a negative net amount purely from fees (no discount involved in making it negative)
            # then we expect income lines.
            logger.warning(
                f"{function_name_for_log}: Invoice {invoice_obj.invoice_number} (Net: {net_invoice_amount_due}) "
                f"has no items with line_type='{InvoiceDetail.LineTypeChoices.FEE_ITEM}' found to credit to income. "
                f"Total Invoice Items: {invoice_obj.details.count()}. Check line_type and amounts of items."
            )
            # This will likely lead to an unbalanced JE if not handled.

        for detail in income_lines_details_qs:
            income_account_for_line = getattr(detail.fee_head, 'income_account', None)
            if not income_account_for_line:
                income_account_for_line = acc_config_profile.default_fee_income_coa
                if not income_account_for_line:
                    raise ValueError(
                        _("Fee Head '%(fh_name)s' is not linked to an Income Account, and no default tenant income account is set.") % 
                        {'fh_name': detail.fee_head.name}
                    )
                logger.warning(f"Using default income account for Fee Head '{detail.fee_head.name}'")
            
            if income_account_for_line.account_type.classification != AccountType.ClassificationChoices.REVENUE:
                logger.warning(
                    f"Fee Head '{detail.fee_head.name}' is linked to a non-REVENUE account '{income_account_for_line}'. "
                    f"Classification: '{income_account_for_line.account_type.get_classification_display()}'"
                )

            # Use the line_total from the InvoiceDetail property
            line_amount_to_credit = detail.line_total 
            if line_amount_to_credit > Decimal('0.00'): # Only credit positive income amounts
                lines_for_je.append({
                    'account': income_account_for_line,
                    'amount': line_amount_to_credit,
                    'entry_type': 'CR',
                    'description': detail.description or \
                                    (_("Income: %(fh_name)s (Inv #%(inv_num)s)") % 
                                    {'fh_name': detail.fee_head.name, 'inv_num': invoice_obj.invoice_number})
                })
                actual_income_credited_debug += line_amount_to_credit
            elif line_amount_to_credit < Decimal('0.00'): # If a "FEE_ITEM" somehow has a negative total
                logger.error(f"Fee item '{detail.description}' for invoice {invoice_obj.invoice_number} has a negative line_total {line_amount_to_credit}. This should be a concession. Not crediting to income.")


        logger.debug(
            f"{function_name_for_log}: JE PREP for Invoice PK {invoice_obj.pk} - "
            f"AR Effect (based on NetInvAmt): {net_invoice_amount_due}, " # This is what AR line will be based on
            f"Discount Dr (based on InvDiscountTotal): {total_discounts_on_invoice}, "
            f"ActualIncomeCredited (Sum of FEE_ITEM line_totals): {actual_income_credited_debug}"
        )
        
        # Expected balance: AR_Debit + Discount_Debit == Income_Credit
        # Or for credit note: AR_Credit == Discount_Credit (if discount reversed) + Income_Debit (if income reversed)

        student_name = str(invoice_obj.student) if invoice_obj.student else _("N/A Student")
        narration = _("Invoice #%(num)s issued to %(stud)s. Total: %(total)s.") % {
            'num': invoice_obj.invoice_number, 
            'stud': student_name,
            'total': net_invoice_amount_due # Use the net invoice amount in narration
        }
        
        return create_and_post_journal_entry(
            user=user, 
            entry_date=je_date or invoice_obj.issue_date,
            narration=narration, 
            lines_data=lines_for_je, 
            entry_classification=JournalEntry.EntryTypeChoices.INVOICE, # Corrected
            source_document_obj=invoice_obj, 
            tenant=tenant_obj
        )
    
    except ValueError as ve: 
        logger.error(f"{function_name_for_log}: VALIDATION ERROR for Invoice {invoice_obj.pk if invoice_obj else 'N/A'}: {ve}")
        raise
    except Exception as e:
        logger.error(f"{function_name_for_log}: UNEXPECTED ERROR creating JE for Invoice {invoice_obj.pk if invoice_obj else 'N/A'}: {e}", exc_info=True)
        raise
    
# Make sure AccountType is defined or imported if used for classification check
# from .models import AccountType (if in the same file as JE, JELine, COA)



# D:\school_fees_saas_v2\apps\accounting\utils.py

from django.utils import timezone
from django.db import transaction
from decimal import Decimal
from django.utils.translation import gettext_lazy as _

# Import your models - adjust paths if necessary
from .models import JournalEntry, JournalLine, Account 
# To avoid circular imports if Payment model imports this util,
# we can use type hinting or pass model instances.
# For this utility, payment_obj will be an instance of Payment.
# from apps.payments.models import Payment # For type hinting if desired
# from apps.schools.models import SchoolProfile # For type hinting

import logging
logger = logging.getLogger(__name__)

def create_payment_journal_entry(payment_obj, school_profile, user):
    """
    Creates and posts a journal entry for a given COMPLETED payment.
    
    Args:
        payment_obj (Payment): The Payment instance.
        school_profile (SchoolProfile): The tenant's SchoolProfile instance with default accounts.
        user (User): The user performing the action (for posted_by).
        
    Returns:
        JournalEntry: The created JournalEntry instance.
        
    Raises:
        ValueError: If essential accounting configurations or accounts are missing.
        Exception: If the journal entry is not balanced after creation.
    """
    logger.info(f"ACC_UTIL: Attempting to create Payment JE for Payment PK {payment_obj.pk} by User {user.email}")

    if payment_obj.amount <= Decimal('0.00'):
        logger.warning(f"ACC_UTIL: Payment amount for PK {payment_obj.pk} is zero or negative. No JE created.")
        return None # Or raise an error if this state is unexpected

    if not school_profile:
        logger.error(f"ACC_UTIL: SchoolProfile not provided for Payment PK {payment_obj.pk}.")
        raise ValueError(_("School accounting profile is required to create payment journal entry."))

    # 1. Determine Debit Account (Cash/Bank)
    if not payment_obj.payment_method or not payment_obj.payment_method.linked_account:
        logger.error(f"ACC_UTIL: Payment method for Payment PK {payment_obj.pk} not linked to CoA asset account.")
        raise ValueError(_("Payment method for Payment PK {pk} is not linked to a Chart of Accounts asset account.").format(pk=payment_obj.pk))
    debit_account = payment_obj.payment_method.linked_account
    logger.debug(f"  Debit Account (Cash/Bank): {debit_account.name} (PK: {debit_account.pk})")

    # 2. Determine Credit Account and Narration
    credit_account = None
    student_name_for_narration = "Unknown Student"
    if payment_obj.student:
        student_name_for_narration = payment_obj.student.get_full_name() if hasattr(payment_obj.student, 'get_full_name') else str(payment_obj.student)

    base_narration = _("Payment ref# %(ref)s from %(stud)s") % {
        'ref': payment_obj.reference_number or payment_obj.pk,
        'stud': student_name_for_narration
    }
    detailed_narration = base_narration # Start with base

    if payment_obj.invoice:
        credit_account = school_profile.default_accounts_receivable
        if not credit_account:
            logger.error(f"ACC_UTIL: Default Accounts Receivable account not set in school profile for Payment PK {payment_obj.pk}.")
            raise ValueError(_("Default Accounts Receivable account not configured for invoice payment."))
        detailed_narration = _("Payment for Invoice #%(inv_num)s by %(stud)s. Ref: %(ref)s") % {
            'inv_num': payment_obj.invoice.invoice_number,
            'stud': student_name_for_narration,
            'ref': payment_obj.reference_number or payment_obj.pk
        }
        logger.debug(f"  Payment for Invoice. Credit Account (A/R): {credit_account.name} (PK: {credit_account.pk})")
    
    elif payment_obj.payment_type == payment_obj.PaymentTypeChoices.PREPAYMENT: # Use model's enum
        credit_account = school_profile.default_unearned_revenue_account
        if not credit_account:
            logger.error(f"ACC_UTIL: Default Unearned Revenue account not set for Prepayment PK {payment_obj.pk}.")
            raise ValueError(_("Default Unearned Revenue account not configured for prepayment."))
        detailed_narration = f"{base_narration} (Prepayment)"
        logger.debug(f"  Prepayment. Credit Account (Unearned Revenue): {credit_account.name} (PK: {credit_account.pk})")

    elif payment_obj.payment_type == payment_obj.PaymentTypeChoices.FEE_PAYMENT and payment_obj.student:
        # Ad-hoc fee payment not against a specific invoice, but for a student.
        # This typically still reduces an overall Accounts Receivable for the student.
        credit_account = school_profile.default_accounts_receivable
        if not credit_account:
            logger.warning(f"ACC_UTIL: Default A/R account not set for ad-hoc fee payment PK {payment_obj.pk}. Attempting fallback to default fee income.")
            credit_account = school_profile.default_fee_income_account # Fallback if you want to recognize income directly
            if not credit_account:
                logger.error(f"ACC_UTIL: Default A/R and Fee Income accounts both not set for ad-hoc fee payment PK {payment_obj.pk}.")
                raise ValueError(_("Neither Default Accounts Receivable nor Default Fee Income account is configured for ad-hoc fee payment."))
        detailed_narration = f"{base_narration} (Ad-hoc Fee Payment)"
        logger.debug(f"  Ad-hoc Fee Payment. Credit Account (A/R or Fee Income): {credit_account.name} (PK: {credit_account.pk})")
        
    elif payment_obj.payment_type == payment_obj.PaymentTypeChoices.OTHER_INCOME:
        # For 'OTHER_INCOME', the specific income account should ideally be selectable on the Payment form
        # or determined by other logic. This is a placeholder.
        # You might need to add a field like 'income_account_for_other' to the Payment model.
        credit_account = school_profile.default_other_income_account # Assuming such a field exists on SchoolProfile
        if not credit_account:
            logger.error(f"ACC_UTIL: Default 'Other Income' account not set for Payment PK {payment_obj.pk}.")
            raise ValueError(_("Default 'Other Income' account not configured. Cannot process 'Other Income' payment type."))
        detailed_narration = f"{base_narration} (Other Income: {payment_obj.notes or 'N/A'})" # Include notes if any
        logger.debug(f"  Other Income. Credit Account: {credit_account.name} (PK: {credit_account.pk})")
        
    else:
        logger.error(f"ACC_UTIL: Unable to determine credit account for Payment PK {payment_obj.pk} with payment_type '{payment_obj.payment_type}'.")
        raise ValueError(_("Cannot determine appropriate credit account for this payment type."))

    # 3. Create Journal Entry and Lines
    with transaction.atomic():
        entry_date = payment_obj.payment_date.date() if payment_obj.payment_date else timezone.now().date()
        
        je = JournalEntry.objects.create(
            date=entry_date,
            narration=detailed_narration[:255], # Ensure narration fits field length
            entry_type=JournalEntry.EntryTypeChoices.PAYMENT, # Use correct enum value
            created_by=user,
            # 'tenant' field on JournalEntry is handled by django-tenants schema isolation
            # If JournalEntry has an explicit tenant FK, it would be:
            # tenant=getattr(payment_obj, 'tenant', None) or payment_obj.invoice.tenant etc.
        )
        logger.debug(f"  Created JournalEntry PK {je.pk}, Number: {je.entry_number}")

        # Debit Line: Cash/Bank Account
        JournalLine.objects.create(
            journal_entry=je,
            account=debit_account,
            debit_amount=payment_obj.amount,
            credit_amount=Decimal('0.00'),
            description=_("Cash/Bank received for payment ref# %(ref)s") % {'ref': payment_obj.transaction_reference or payment_obj.pk}
        )
        logger.debug(f"    Created Debit Line: Account {debit_account.name}, Amount {payment_obj.amount}")

        # Credit Line: A/R, Unearned Revenue, or Other Income
        JournalLine.objects.create(
            journal_entry=je,
            account=credit_account,
            debit_amount=Decimal('0.00'),
            credit_amount=payment_obj.amount,
            description=detailed_narration[:255] # Can reuse or make more specific
        )
        logger.debug(f"    Created Credit Line: Account {credit_account.name}, Amount {payment_obj.amount}")
        
        # Check if entry is balanced (assuming JournalEntry has such a method)
        is_balanced, balance_difference = je.is_entry_balanced() # Assumes this method exists on JournalEntry
        if not is_balanced:
            logger.critical(f"ACC_UTIL: Journal Entry PK {je.pk} for Payment PK {payment_obj.pk} is NOT BALANCED. Difference: {balance_difference}. Transaction will be rolled back.")
            # The exception will cause the transaction.atomic() block to roll back.
            raise Exception(f"Journal Entry {je.pk} for Payment {payment_obj.pk} is not balanced. Difference: {balance_difference}.")
        
        logger.info(f"ACC_UTIL: Journal Entry PK {je.pk} (Number: {je.entry_number}) created and balanced successfully for Payment PK {payment_obj.pk}.")
        return je
    
    

def create_expense_journal_entry(user, expense_obj, tenant_obj): # Added tenant_obj
    logger.info(f"UTIL: Creating Expense JE for Expense PK {expense_obj.pk}")
    acc_config = get_tenant_accounting_config(tenant_obj)

    if not expense_obj.category or not expense_obj.category.expense_account:
        raise ValueError(_("Expense Category '%(cat)s' not linked to Expense Account.") % {'cat': expense_obj.category.name if expense_obj.category else 'N/A'})
    expense_coa_account = expense_obj.category.expense_account

    paid_from_acc = expense_obj.paid_from_account if hasattr(expense_obj, 'paid_from_account') and expense_obj.paid_from_account else None
    if not paid_from_acc and expense_obj.payment_method and expense_obj.payment_method.linked_account:
        paid_from_acc = expense_obj.payment_method.linked_account
    if not paid_from_acc: # Fallback
        paid_from_acc = acc_config.get('cash_account') or acc_config.get('bank_account')
        if not paid_from_acc: raise ValueError(_("Expense payment account not found (specific or default)."))
            
    if expense_obj.amount <= Decimal('0.00'): return None

    lines_for_je = [
        {'account': expense_coa_account, 'amount': expense_obj.amount, 'entry_type': 'DR', 'description': _("Expense incurred")},
        {'account': paid_from_acc, 'amount': expense_obj.amount, 'entry_type': 'CR', 'description': _("Payment for expense")}
    ]
    narration = _("Expense: %(desc)s") % {'desc': expense_obj.description[:50] or expense_obj.category.name}
    return create_and_post_journal_entry(user, expense_obj.expense_date, narration, lines_for_je, 'EXPENSE', expense_obj, tenant=tenant_obj)





# D:\school_fees_saas_v2\apps\accounting\utils.py
from decimal import Decimal
from django.utils import timezone
from django.db import transaction
from django.conf import settings # For AUTH_USER_MODEL
from apps.accounting.models import JournalEntry, JournalLine, Account # Import necessary models
from apps.fees.models import Invoice # Import Invoice if needed for type hinting or direct access
# Import other models if needed, e.g., StaffUser for created_by

import logging
logger = logging.getLogger(__name__)

def create_invoice_journal_entry(invoice_instance, created_by_user, Narration=None): # Match signature
    """
    Creates journal entries for a given invoice.
    Assumes invoice_instance is a saved Invoice object.
    Assumes created_by_user is a User/StaffUser instance.
    """
    if not invoice_instance:
        logger.error("create_invoice_journal_entry: No invoice_instance provided.")
        return None

    # Determine accounts (These should be configurable or fetched based on settings/school profile)
    # Example: Using fixed account codes/names for now - BAD PRACTICE for production
    try:
        # These accounts MUST exist in the Chart of Accounts for the current tenant
        accounts_receivable_acc = Account.objects.get(code="AR_CTRL") # Or by name/type
        tuition_revenue_acc = Account.objects.get(code="TUIT_REV") # Or by name/type
        # Add other revenue accounts if your invoice details can map to different revenue types
    except Account.DoesNotExist as e:
        logger.error(f"create_invoice_journal_entry: Required account not found: {e}. Cannot create JE for Invoice {invoice_instance.invoice_number}.")
        # Consider raising an exception or returning a specific error indicator
        return None 
    except Exception as e_acc: # Catch other potential errors like MultipleObjectsReturned
        logger.error(f"create_invoice_journal_entry: Error fetching accounts: {e_acc}. Cannot create JE for Invoice {invoice_instance.invoice_number}.")
        return None


    total_invoice_amount = invoice_instance.total_amount # Assuming total_amount is the net amount to be recognized

    if total_invoice_amount <= Decimal('0.00'):
        logger.info(f"create_invoice_journal_entry: Invoice {invoice_instance.invoice_number} has zero or negative total amount ({total_invoice_amount}). No JE created.")
        return None

    with transaction.atomic():
        try:
            je = JournalEntry.objects.create(
                entry_date=invoice_instance.date_issued, # Or transaction_date if different
                description=Narration or f"Journal entry for Invoice #{invoice_instance.invoice_number} issued to student {invoice_instance.student.full_name}",
                entry_type=JournalEntry.EntryType.INVOICE_GENERATED, # Use your defined choices
                status=JournalEntry.EntryStatus.POSTED, # Invoices usually result in POSTED JEs
                created_by=created_by_user,
                # tenant=invoice_instance.tenant # If JournalEntry is a shared model with tenant FK
            )

            # Debit Accounts Receivable
            JournalEntryLine.objects.create(
                journal_entry=je,
                account=accounts_receivable_acc,
                debit_amount=total_invoice_amount,
                credit_amount=Decimal('0.00'),
                description=f"Accounts Receivable for Invoice #{invoice_instance.invoice_number}"
            )

            # Credit Revenue Account(s)
            # This is simplified. If your invoice has multiple line items pointing to different revenue accounts,
            # you would iterate through invoice_instance.details.all() and create corresponding credit lines.
            JournalEntryLine.objects.create(
                journal_entry=je,
                account=tuition_revenue_acc, # Assuming all is tuition revenue for this example
                debit_amount=Decimal('0.00'),
                credit_amount=total_invoice_amount,
                description=f"Tuition Revenue for Invoice #{invoice_instance.invoice_number}"
            )
            
            # Optional: Link JE back to Invoice if your Invoice model has a FK to JournalEntry
            if hasattr(invoice_instance, 'journal_entry_link_field_name'): # Replace with actual field name
                setattr(invoice_instance, 'journal_entry_link_field_name', je)
                invoice_instance.save(update_fields=['journal_entry_link_field_name'])

            logger.info(f"Successfully created Journal Entry {je.entry_number} for Invoice {invoice_instance.invoice_number}")
            return je
        except Exception as e:
            logger.error(f"Error creating journal entry for Invoice {invoice_instance.invoice_number}: {e}", exc_info=True)
            # Transaction will be rolled back due to 'with transaction.atomic()'
            return None
# Make sure the function name 'create_invoice_journal_entry' matches EXACTLY.