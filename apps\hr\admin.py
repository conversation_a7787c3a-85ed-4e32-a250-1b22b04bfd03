# D:\school_fees_saas_v2\apps\hr\admin.py
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

# Import models from this app (hr)
from .models import LeaveType, LeaveRequest, EmployeeProfile

# D:\school_fees_saas_v2\apps\hr\admin.py
from django.contrib import admin
from .models import LeaveType, LeaveRequest, LeaveBalance, LeaveBalanceLog # Import all models


from django.contrib import admin
from .models import (
    SalaryGrade, 
    SalaryGradeComponent,
    SalaryComponent,
    StaffSalaryStructure,
    SalaryStructureComponent,
    PayrollRun,
    Payslip,
    # --- IMPORT YOUR NEW MODELS ---
    TaxBracket,
    StatutoryDeduction,
)
@admin.register(LeaveType)
class LeaveTypeAdmin(admin.ModelAdmin):
    # --- THIS IS THE CORRECTED 'list_display' ---
    list_display = (
        'name', 
        'is_paid', 
        'is_active', 
        'accrual_frequency', 
        'accrual_rate', 
        'max_accrual_balance',
        'max_days_per_year_grant'
    )
    list_filter = ('is_paid', 'is_active', 'accrual_frequency')
    search_fields = ('name',)
    
    # Use fieldsets to organize the form in the admin for better usability
    fieldsets = (
        ('General Information', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Leave Policy', {
            'fields': ('is_paid', 'requires_approval')
        }),
        ('Leave Granting (for non-accruing types)', {
            'description': "Use this for leave types like 'Sick Leave' where a set number of days is granted annually.",
            'fields': ('max_days_per_year_grant',)
        }),
        ('Automated Accrual System', {
            'description': "Use these settings for leave types like 'Annual Leave' that are earned over time.",
            'fields': ('accrual_frequency', 'accrual_rate', 'max_accrual_balance', 'prorate_accrual')
        }),
    )



@admin.register(LeaveRequest)
class LeaveRequestAdmin(admin.ModelAdmin):
    """
    Admin interface for managing staff leave requests.
    Updated to use the consolidated LeaveRequest model fields.
    """
    
    # --- Core Display and Filtering ---
    list_display = (
        'get_employee_display',
        'leave_type',
        'start_date',
        'end_date',
        'duration', # Use the 'duration' field directly
        'status',
        'get_approver_name',
        'status_changed_at', # Use the new field name
    )
    list_filter = (
        'status',
        'leave_type',
        'start_date',
        'employee__designation', # Filter by designation on the StaffUser model
    )
    search_fields = (
        'employee__user__first_name',
        'employee__user__last_name',
        'employee__user__email',
        'employee__user__employee_id',
        'leave_type__name',
    )
    list_select_related = ('employee__user', 'leave_type', 'approved_by__user')
    date_hierarchy = 'start_date'
    
    # Use autocomplete for ForeignKey fields with many choices
    autocomplete_fields = ['employee', 'approved_by']
    
    # --- Form Layout (Fieldsets) ---
    fieldsets = (
        ("Request Details", {
            'fields': ('employee', 'leave_type', 'status', 'reason', 'attachment')
        }),
        ("Leave Period", {
            'fields': (('start_date', 'end_date'), ('half_day_start', 'half_day_end'), 'duration')
        }),
        ("Approval Information", {
            # Use the corrected field names
            'fields': ('approved_by', 'status_changed_at', 'status_reason')
        }),
        ("Timestamps", {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',) # Makes this section collapsible
        }),
    )
    
    # --- Read-only Fields ---
    # Fields that should not be edited directly in the admin form
    readonly_fields = (
        'duration', # This is calculated automatically by the model's save() method
        'created_at', 
        'updated_at', 
        'status_changed_at',
        'approved_by', # This should be set by your approval/rejection logic, not manually
    )

    # --- Custom Admin Methods ---
    @admin.display(description='Employee', ordering='employee__user__last_name')
    def get_employee_display(self, obj):
        # This method is good, just ensures it handles the employee/user relationship gracefully
        if obj.employee and hasattr(obj.employee, 'user') and obj.employee.user:
            user = obj.employee.user
            emp_id_str = f"({user.employee_id})" if user.employee_id else ""
            return f"{user.get_full_name()} {emp_id_str}"
        return "N/A"

    @admin.display(description='Approver', ordering='approved_by__user__last_name')
    def get_approver_name(self, obj):
        if obj.approved_by and hasattr(obj.approved_by, 'user') and obj.approved_by.user:
            return obj.approved_by.user.get_full_name()
        return "—"

# @admin.register(LeaveRequest)
# class LeaveRequestAdmin(admin.ModelAdmin):
#     """
#     Admin interface for managing staff leave requests.
#     """
#     # --- Core Display and Filtering ---
#     list_display = (
#         'get_employee_display',
#         'leave_type',
#         'start_date',
#         'end_date',
#         'days_requested_display',
#         'status',
#         'get_approver_name',
#     )
#     # --- CORRECTED list_filter ---
#     list_filter = (
#         'status',
#         'leave_type',
#         'start_date',
#         'employee__designation', # Correct Path: LeaveRequest -> EmployeeProfile -> designation
#     )
#     search_fields = (
#         'employee__user__first_name', # Correct: LeaveRequest -> EmployeeProfile -> User -> first_name
#         'employee__user__last_name',
#         'employee__user__email',
#         'employee__employee_id',      # Correct: LeaveRequest -> EmployeeProfile -> employee_id
#         'leave_type__name',
#     )
#     list_select_related = ('employee__user', 'leave_type', 'approved_by__user') # Pre-fetch user for approver too
#     date_hierarchy = 'start_date'

#     # --- Form Layout and Fields ---
#     autocomplete_fields = ['employee', 'approved_by']
    
#     fieldsets = (
#         ("Request Details", {
#             'fields': ('employee', 'leave_type', 'status', 'reason', 'attachment')
#         }),
#         ("Leave Period", {
#             'fields': (('start_date', 'end_date'), ('half_day_start', 'half_day_end'), 'days_requested_display')
#         }),
#         ("Approval Information", {
#             'fields': ('approved_by', 'approval_date', 'admin_notes')
#         }),
#         ("Timestamps", {
#             'fields': ('created_at', 'updated_at'),
#             'classes': ('collapse',)
#         }),
#     )
    
#     readonly_fields = ('days_requested_display', 'created_at', 'updated_at', 'approval_date')

#     # --- Custom Admin Methods ---
#     @admin.display(description='Employee', ordering='employee__user__last_name')
#     def get_employee_display(self, obj):
#         if obj.employee and obj.employee.user:
#             user = obj.employee.user
#             # Use the employee_id from the profile now
#             emp_id_str = f"({obj.employee.employee_id})" if obj.employee.employee_id else ""
#             return f"{user.get_full_name() or user.email} {emp_id_str}"
#         return "N/A"

#     @admin.display(description='Approver', ordering='approved_by__user__last_name')
#     def get_approver_name(self, obj):
#         if obj.approved_by and obj.approved_by.user:
#             return obj.approved_by.user.get_full_name()
#         return "—"

#     @admin.display(description='Days Requested')
#     def days_requested_display(self, obj):
#         # Assumes your LeaveRequest model has a method/property 'days_requested'
#         if hasattr(obj, 'days_requested'):
#             return obj.days_requested
#         return "N/A"



from django.contrib import admin
from .models import LeaveBalance # and your other HR models
# D:\school_fees_saas_v2\apps\hr\admin.py

@admin.register(LeaveBalance)
class LeaveBalanceAdmin(admin.ModelAdmin):
    """
    Admin interface for managing staff leave balances.
    """
    list_display = (
        'employee', 
        'leave_type', 
        'year',
        'days_accrued',
        'days_taken_display',
        'days_available_display'
    )
    list_filter = ('year', 'leave_type', 'employee')
    search_fields = ('employee__user__first_name', 'employee__user__last_name', 'year')
    list_select_related = ('employee__user', 'leave_type')
    
    # --- CORRECTED READONLY & FORM LAYOUT ---
    
    # The fields an admin can actually edit
    # fields = ('employee', 'leave_type', 'year', 'days_accrued')

    # The fields that are for display only and are calculated
    readonly_fields = ('days_taken_display', 'days_available_display')
    
    # fieldsets can combine editable and readonly fields for a nice layout
    fieldsets = (
        ('Assignment', {
            'fields': ('employee', 'leave_type', 'year')
        }),
        ('Balance Information', {
            'fields': ('days_accrued', 'days_taken_display', 'days_available_display')
        }),
    )

    # --- Custom Admin Methods ---
    @admin.display(description='Days Taken')
    def days_taken_display(self, obj):
        return obj.days_taken

    @admin.display(description='Days Available')
    def days_available_display(self, obj):
        return obj.days_available

# @admin.register(LeaveBalance)
# class LeaveBalanceAdmin(admin.ModelAdmin):
#     """
#     Admin interface for managing staff leave balances.
#     """
#     # Use the correct field names and custom display methods
#     list_display = (
#         'employee',  # Custom method to display the employee's name
#         'leave_type',
#         'year',
#         'days_accrued',
#         'days_taken_display',
#         'days_available_display' # Custom method for the calculated property
#         # 'updated_at',
#     )
    
#     list_filter = (
#         'year',
#         'leave_type', 
#         'employee'
#     )
    
#     # Update search fields to use the correct relationship path
#     search_fields = (
#         'employee__user__first_name',
#         'employee__user__last_name',
#         'year',
#     )
    
#     list_select_related = ('employee__user', 'leave_type')
    
#     # Use 'autocomplete_fields' for a better user experience than raw_id_fields
#     # This requires EmployeeProfileAdmin to have search_fields defined.
#     autocomplete_fields = ('employee', 'leave_type')
    
#     # Make calculated fields readonly in the detail view
#     readonly_fields = ('get_days_remaining', 'created_at', 'updated_at')
    
#     # For a better form layout in the admin
#     fieldsets = (
#         (None, {
#             'fields': ('employee', 'leave_type', 'year_or_period_info')
#         }),
#         ('Balance Details', {
#             'fields': ('days_accrued', 'days_taken', 'get_days_remaining')
#         }),
#         ('Timestamps', {
#             'fields': ('last_accrual_date', 'created_at', 'updated_at'),
#             'classes': ('collapse',)
#         }),
#     )

#     @admin.display(description='Days Taken')
#     def days_taken_display(self, obj):
#         # 'obj' is the LeaveBalance instance.
#         # This calls the @property from your model.
#         return obj.days_taken

#     @admin.display(description='Days Available')
#     def days_available_display(self, obj):
#         # This calls the @property from your model.
#         return obj.days_available
    
    
#     # @admin.display(description='Employee', ordering='employee__user__last_name')
#     # def get_employee_name(self, obj):
#     #     """
#     #     Creates a readable string for the employee from the related EmployeeProfile.
#     #     """
#     #     if obj.employee and hasattr(obj.employee, 'user') and obj.employee.user:
#     #         return obj.employee.user.get_full_name() or obj.employee.user.email
#     #     return "N/A"

#     # @admin.display(description='Days Remaining', ordering='days_accrued') # Can't sort on property directly, so sort by a real field
#     # def get_days_remaining(self, obj):
#     #     """
#     #     Displays the result of the `days_remaining` model property.
#     #     """
#     #     return obj.days_remaining



@admin.register(LeaveBalanceLog)
class LeaveBalanceLogAdmin(admin.ModelAdmin):
    list_display = ('leave_balance', 'action', 'change_amount', 'created_at')
    list_filter = ('action',)
    search_fields = ('leave_balance__staff_user__first_name', 'leave_balance__leave_type__name')
    raw_id_fields = ('leave_balance',)
    


# D:\school_fees_saas_v2\apps\hr\admin.py
from django.contrib import admin
from .models import EmployeeProfile # ... and other models



@admin.register(EmployeeProfile)
class EmployeeProfileAdmin(admin.ModelAdmin):
    # --- Display ---
    list_display = (
        'get_user_full_name', 
        'get_user_email', 
        'employee_id',      # Now a direct field
        'designation',      # Now a direct field
        'employment_type', 
        'date_hired',
        'is_active_status' # Custom method to check user's active status
    )
    list_select_related = ('user',) # Essential for performance

    # --- Search & Filter ---
    search_fields = (
        'user__first_name', 
        'user__last_name', 
        'user__email',
        'employee_id',
        'designation',
    )
    list_filter = (
        'employment_type',
        'designation',      # Corrected: Filter directly on this model's field
        'user__is_active',  # Correct: Filter on the related user's status
    )
    ordering = ('user__last_name', 'user__first_name')
    raw_id_fields = ('user',) # Good for performance

    # --- Form Layout ---
    fieldsets = (
        ('Staff Account Link', {'fields': ('user',)}), 
        ('Personal Details', {'fields': ('middle_name', 'gender', 'date_of_birth', 'marital_status', 'photo')}),
        ('Contact & Address', {'fields': ('phone_number_alternate', 'address_line1', 'address_line2', 'city', 'state_province', 'postal_code', 'country')}),
        ('Employment Details', {'fields': ('employee_id', 'designation', 'employment_type', 'date_hired', 'date_left')}),
        ('Internal Notes', {'fields': ('notes',)}),
        ('Timestamps', {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)}),
    )
    readonly_fields = ('created_at', 'updated_at')

    # --- Custom Display Methods ---
    @admin.display(description='Employee Name', ordering='user__last_name')
    def get_user_full_name(self, obj):
        return obj.user.get_full_name() if obj.user else "N/A"

    @admin.display(description='Email', ordering='user__email')
    def get_user_email(self, obj):
        return obj.user.email if obj.user else "N/A"

    @admin.display(description='Active', ordering='user__is_active', boolean=True)
    def is_active_status(self, obj):
        return obj.user.is_active if obj.user else False

# @admin.register(EmployeeProfile)
# class EmployeeProfileAdmin(admin.ModelAdmin):
#     list_display = (
#         'get_user_full_name', 
#         'get_user_email', 
#         'get_user_employee_id', # Method to display from StaffUser
#         'get_user_designation', # Method to display from StaffUser
#         'employment_type', 
#         'updated_at'
#     )
#     search_fields = ( # Fields used for autocomplete when EmployeeProfile is a ForeignKey
#         'user__first_name', 
#         'user__last_name', 
#         'user__email',
#         'user__employee_id', # Assuming StaffUser has employee_id
#     )
#     list_filter = ('employment_type', 'user__is_active', 'user__designation') # Filter by StaffUser's designation
#     list_select_related = ('user',) # Crucial for performance of display methods
#     ordering = ('user__last_name', 'user__first_name')
#     raw_id_fields = ('user',) # Often better than dropdown for OneToOne to a large User table

#     fieldsets = (
#         # 'user' is the PK, so it's not typically in fieldsets unless you want to make it selectable
#         # when creating a new EmployeeProfile (if not primary_key=True).
#         # Since it's primary_key=True linked to StaffUser, it's implicitly handled.
#         # If you need to select the StaffUser when creating an EmployeeProfile (if not pk):
#         # (None, {'fields': ('user',)}), 
#         ('Personal Details', {'fields': ('middle_name', 'gender', 'date_of_birth', 'marital_status', 'photo')}),
#         ('Contact & Address', {'fields': ('phone_number_alternate', 'address_line1', 'address_line2', 'city', 'state_province', 'postal_code', 'country')}),
#         ('Employment Details', {'fields': ('employment_type', 'date_left')}),
#         ('Internal Notes', {'fields': ('notes',)}),
#         ('Timestamps', {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)}),
#     )
#     readonly_fields = ('created_at', 'updated_at')

#     @admin.display(description='Employee Name', ordering='user__last_name')
#     def get_user_full_name(self, obj):
#         return obj.user.get_full_name() if obj.user else "N/A"

#     @admin.display(description='Email', ordering='user__email')
#     def get_user_email(self, obj):
#         return obj.user.email if obj.user else "N/A"

#     @admin.display(description='Employee ID', ordering='user__employee_id') # Assuming StaffUser.employee_id
#     def get_user_employee_id(self, obj):
#         return obj.user.employee_id if obj.user and hasattr(obj.user, 'employee_id') else "N/A"

#     @admin.display(description='Designation', ordering='user__designation') # Assuming StaffUser.designation
#     def get_user_designation(self, obj):
#         return obj.user.designation if obj.user and hasattr(obj.user, 'designation') else "N/A"




# D:\school_fees_saas_v2\apps\hr\admin.py

from django.contrib import admin
from .models import LeaveType, LeaveRequest, LeaveBalance, PayrollRun, Payslip



class PayslipInline(admin.TabularInline):
    """Allows viewing (but not easy editing of) payslips within a PayrollRun."""
    model = Payslip
    # List the fields you want to see in the inline view
    fields = ('staff_member', 'gross_earnings', 'total_deductions', 'net_pay')
    readonly_fields = fields # Make them read-only here
    can_delete = False # You shouldn't delete payslips from here
    extra = 0 # Don't show any extra empty forms
    show_change_link = True # Allow clicking to the full payslip change form



@admin.register(PayrollRun)
class PayrollRunAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'pay_period_start', 'pay_period_end', 'payment_date', 'status', 'processed_by', 'processed_at')
    list_filter = ('status', 'payment_date')
    search_fields = ('notes',)
    inlines = [PayslipInline] # This will show linked payslips at the bottom
    readonly_fields = ('processed_by', 'processed_at', 'created_at', 'updated_at')



@admin.register(Payslip)
class PayslipAdmin(admin.ModelAdmin):
    list_display = ('staff_member', 'payroll_run', 'net_pay', 'gross_earnings', 'total_deductions')
    list_filter = ('payroll_run', 'staff_member')
    search_fields = ('staff_member__first_name', 'staff_member__last_name', 'staff_member__email')
    
    # Make calculated fields read-only in the admin
    readonly_fields = ('gross_earnings', 'total_deductions', 'net_pay', 'created_at')
    
    # Organize fields into sections for better usability
    fieldsets = (
        (None, {
            'fields': ('payroll_run', 'staff_member')
        }),
        ('Earnings', {
            'fields': ('basic_salary', 'allowances', 'bonuses')
        }),
        ('Deductions', {
            'fields': ('tax_deductions', 'pension_deductions', 'loan_repayments', 'other_deductions')
        }),
        ('Summary (Auto-calculated)', {
            'classes': ('collapse',),
            'fields': ('gross_earnings', 'total_deductions', 'net_pay')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
    )



@admin.register(TaxBracket)
class TaxBracketAdmin(admin.ModelAdmin):
    # Use the EXACT field names from your models.py file
    list_display = (
        'name', 
        'from_amount', 
        'to_amount', 
        'rate_percent', 
        'deduction_amount', 
        'is_active'
    )
    
    # Make some fields editable directly in the list view for convenience
    list_editable = (
        'from_amount',
        'to_amount',
        'rate_percent',
        'deduction_amount',
        'is_active'
    )

    # The ordering field MUST be one of the fields in the model
    ordering = ('from_amount',)
    
    

@admin.register(StatutoryDeduction)
class StatutoryDeductionAdmin(admin.ModelAdmin):
    list_display = ('name', 'employee_contribution_rate', 'employer_contribution_rate', 'is_active')
    list_editable = ('employee_contribution_rate', 'employer_contribution_rate', 'is_active')