{% extends "./_pdf_base.html" %}
{% load humanize %}

{% block pdf_title %}Collection Report - {{ school_profile.name|default:"School" }}{% endblock %}

{% block report_period_header %}
    {% if filter_form_data.start_date and filter_form_data.end_date %}
        <p class="mb-0">Period: {{ filter_form_data.start_date|date:"d M Y" }} to {{ filter_form_data.end_date|date:"d M Y" }}</p>
    {% elif filter_form_data.start_date %}
        <p class="mb-0">From: {{ filter_form_data.start_date|date:"d M Y" }}</p>
    {% elif filter_form_data.end_date %}
        <p class="mb-0">Up to: {{ filter_form_data.end_date|date:"d M Y" }}</p>
    {% endif %}
{% endblock %}

{% block pdf_content %}
    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Receipt No.</th>
                <th>Student Name</th>
                <th>Adm. No.</th>
                <th>Invoice No.</th>
                <th class="text-end">Amount Paid</th>
                <th>Method</th>
            </tr>
        </thead>
        <tbody>
            {% for payment in report_data %}
            <tr>
                <td>{{ payment.payment_date|date:"d M Y" }}</td>
                <td>{{ payment.receipt_number }}</td>
                <td>{{ payment.invoice.student.full_name|default:"N/A" }}</td>
                <td>{{ payment.invoice.student.admission_number|default:"N/A" }}</td>
                <td>{{ payment.invoice.invoice_number|default:"N/A" }}</td>
                <td class="text-end">{{ payment.amount|floatformat:2|intcomma }}</td>
                <td>{{ payment.payment_method.name|default:"N/A" }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="text-center">No payments found.</td>
            </tr>
            {% endfor %}
        </tbody>
        {% if report_data %}
        <tfoot>
            <tr>
                <th colspan="5" class="text-end fw-bold">Total Collected:</th>
                <th class="text-end fw-bold">{{ total_collected_all_pages|default:"0.00"|floatformat:2|intcomma }}</th>
                <th></th>
            </tr>
        </tfoot>
        {% endif %}
    </table>
{% endblock %}

