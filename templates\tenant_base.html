{# D:\school_fees_saas_v2\templates\tenant_base.html - Incorporating LIME BANNER & Fees Mgt fix #}
{% extends "base.html" %}

{% load static i18n humanize widget_tweaks core_tags string_utils nav_utils form_tags %}

{% block title %}
    {% block tenant_page_title %}Admin Dashboard{% endblock tenant_page_title %}
    - {{ request.tenant.name|default:"School Portal" }}
{% endblock title %}

{% block page_specific_css %}
    {{ block.super }}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="{% static 'css/tenant_form_styles.css' %}">
    <style>
        /* ... your existing styles ... */
        .main-content-area {
            padding-top: 1rem;
            padding-bottom: 70px;
            min-height: calc(100vh - 56px - 56px);
        }
        .status-badge { font-size: 0.8em; padding: 0.4em 0.7em; }
        .actions-column .btn { margin-right: 0.25rem; margin-bottom: 0.25rem; }
        .navbar .dropdown-menu { z-index: 1031; }
        .global-filter-form .form-select-sm { max-width: 220px; font-size: 0.875rem; }
        .global-filter-form label { display: none; }

        .debug-info-block { font-size: 0.85rem; opacity: 0.9; background-color: #f8f9fa; border: 1px solid #dee2e6; margin-top: 20px; padding: 15px; border-radius: .25rem; }
        .debug-info-block h6 { margin-top: 0.5rem; margin-bottom: 0.25rem; font-weight: bold; }
        .debug-info-block p { margin-bottom: 0.25rem; word-break: break-all; }
        .debug-info-block ul { padding-left: 20px; margin-bottom: 0.5rem; }
        .debug-permissions-table { font-size: 0.8rem; margin-top: 10px; width: 100%; }
        .debug-permissions-table th, .debug-permissions-table td { padding: 3px 6px; border: 1px solid #ddd; text-align: left; }
        .debug-permissions-table th { background-color: #e9ecef; }
        .perm-true { color: #198754; font-weight: bold; }
        .perm-false { color: #dc3545; }
    </style>
    {% block extra_tenant_css %}{% endblock extra_tenant_css %}

{% endblock page_specific_css %}

{% block navbar %}
<nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-navbar shadow-sm">
    <div class="container-fluid">
        {# --- Navbar Brand --- #}
        <a class="navbar-brand fw-bold"
        href="{% if user_type_flags.IS_TENANT_STAFF_USER %}{% url 'schools:dashboard' %}{% elif user_type_flags.IS_TENANT_PARENT_USER and tenant_features.PARENT_PORTAL %}{% url 'parent_portal:dashboard' %}{% else %}/{% endif %}">
            {% if request.tenant.schoolprofile.logo %}
                <img src="{{ request.tenant.schoolprofile.logo.url }}" alt="{{ request.tenant.name }} Logo" height="30" class="d-inline-block align-top me-2 rounded">
            {% else %}
                <i class="bi bi-building me-2"></i>
            {% endif %}
            {{ request.tenant.name|default:"Portal" }}
        </a>

        {# --- Navbar Toggler --- #}
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#tenantAppNavbar" aria-controls="tenantAppNavbar" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        {# --- Collapsible Navbar Content --- #}
        <div class="collapse navbar-collapse" id="tenantAppNavbar">
            {% with current_app_name=request.resolver_match.app_name current_url_name=request.resolver_match.url_name current_view_name=request.resolver_match.view_name %} {% endwith %}

            {# === LEFT-ALIGNED NAVIGATION ITEMS (me-auto) === #}
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">

                {# --- STAFF USER MAIN NAVIGATION --- #}
                {% if user_type_flags.IS_TENANT_STAFF_USER %}
                    {% if navbar_flags.show_dashboard_link %}
                        <li class="nav-item">
                            <a class="nav-link {% if current_view_name == 'schools:dashboard' %}active{% endif %}" href="{% url 'schools:dashboard' %}"><i class="bi bi-grid-1x2-fill me-1"></i>Dashboard</a>
                        </li>
                    {% endif %}
                {% endif %}
                    {% comment %} {% if navbar_flags.show_manage_students_link %}
                        <li class="nav-item"><a class="nav-link {% if current_app_name == 'students' %}active{% endif %}" href="{% url 'students:student_list' %}"><i class="bi bi-people-fill me-1"></i>Students</a></li>
                    {% endif %}  {% endcomment %}


                    {# Student Management Dropdown (Example) #}
                    {% if navbar_flags.show_manage_students_link or perms.students.view_parentuser %} {# Modify condition #}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'students' %}active{% endif %}"
                        href="#" id="studentsMgmtDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-people-fill me-1"></i> {% trans "Students & Parents" %}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="studentsMgmtDropdown">
                            {% if navbar_flags.show_manage_students_link %}
                                <li><a class="dropdown-item {% if request.resolver_match.url_name == 'student_list' or 'student_detail' in request.resolver_match.url_name or 'student_update' in request.resolver_match.url_name or 'student_create' in request.resolver_match.url_name %}active{% endif %}"
                                    href="{% url 'students:student_list' %}">{% trans "Manage Students" %}</a></li>
                            {% endif %}
                            {% if perms.students.view_parentuser %} {# Or a specific navbar_flag #}
                                <li><a class="dropdown-item {% if request.resolver_match.url_name == 'parentuser_list' %}active{% endif %}"
                                    href="{% url 'students:parentuser_list' %}">{% trans "Manage Parent Accounts" %}</a></li>
                            {% endif %}
                        </ul>
                    </li>
                    {% endif %}


                <!-- Fees Management Dropdown -->
                {% if navbar_flags.show_fees_management_dropdown %}
                <li class="nav-item dropdown">
                    {# The dropdown is active if the current app is 'fees' #}
                    <a class="nav-link dropdown-toggle {% is_active_nav '' 'fees' %}"
                    href="#" id="feesNavDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-receipt-cutoff me-1"></i>Fees Mgt
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="feesNavDropdown">
                        
                        {# Invoices Link #}
                        {% if navbar_flags.show_invoices_link %}
                            <li>
                                <a class="dropdown-item {% is_active_nav 'fees:invoice_list,fees:invoice_detail_view,fees:invoice_create,fees:invoice_update' %}"
                                href="{% url 'fees:invoice_list' %}">
                                    Manage Invoices
                                </a>
                            </li>
                        {% endif %}

                        {# Concession Types Link #}
                        {% if navbar_flags.show_concessions_link %}
                            <li>
                                <a class="dropdown-item {% is_active_nav 'fees:concession_type_list' %}"
                                href="{% url 'fees:concession_type_list' %}">
                                    Manage Concessions
                                </a>
                            </li>
                        {% endif %}
                        
                    </ul>
                </li>
                {% endif %}

                        <!-- HR & Staff Dropdown -->
                    {% if navbar_flags.show_hr_staff_dropdown %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'hr' or 'staff' in request.resolver_match.url_name %}active{% endif %}"
                                href="#" id="hrStaffNavDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-badge me-1"></i>HR & Staff
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="hrStaffNavDropdown">


                                {# --- NEW PAYROLL LINK --- #}
                                {% if perms.hr.manage_payroll %} {# Use the custom permission from the PayrollRun model #}
                                    <li>
                                        <a class="dropdown-item {% if 'payroll' in request.resolver_match.view_name %}active{% endif %}"
                                        href="{% url 'hr:payroll_run_list' %}">
                                            <i class="bi bi-calculator-fill me-2"></i>{% trans "Manage Payroll" %}
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                {% endif %}
                                
                                {# --- STAFF & LEAVE MANAGEMENT --- #}
                                {% if navbar_flags.show_manage_staff_users_link %}
                                    <li><a class="dropdown-item" href="{% url 'schools:staff_list' %}">Manage Staff</a></li>
                                {% endif %}
                                {% if navbar_flags.show_leave_management_link %}
                                    <li><a class="dropdown-item" href="{% url 'hr:admin_leaverequest_list' %}">Approve Leave</a></li>
                                {% endif %}

                                {# --- ADD THE NEW LINK HERE --- #}
                                {# Add a divider if there are other items above it #}
                                {% if navbar_flags.show_manage_staff_users_link or navbar_flags.show_leave_management_link %}
                                    <li><hr class="dropdown-divider"></li>
                                {% endif %}

                                {% if navbar_flags.show_manage_leave_balances_link %}
                                    <li>
                                        <a class="dropdown-item {% if request.resolver_match.view_name == 'hr:leave_balance_list' %}active{% endif %}" 
                                        href="{% url 'hr:leave_balance_list' %}">
                                            <i class="bi bi-person-check-fill me-2"></i>{% trans "Manage Leave Balances" %}
                                        </a>
                                    </li>
                                {% endif %}
                                {# --- END OF NEW LINK --- #}

                                {% if navbar_flags.show_leave_type_management_link %}
                                    <li>
                                        <a class="dropdown-item {% if 'leavetype' in request.resolver_match.view_name %}active{% endif %}" 
                                        href="{% url 'hr:leavetype_list' %}">
                                            Manage Leave Types
                                        </a>
                                    </li>
                                {% endif %}

                                {% if navbar_flags.show_staff_leave_request_link %}
                                    <li><a class="dropdown-item" href="{% url 'hr:staff_leaverequest_list' %}">My Leave Requests</a></li>
                                {% endif %}

                                {# --- SEPARATOR AND PAYROLL SETUP SECTION --- #}
                                {# Show a divider if there are leave links above AND payroll links below #}
                                {% if navbar_flags.show_manage_staff_users_link or navbar_flags.show_leave_management_link and navbar_flags.show_salary_grades_link or navbar_flags.show_salary_components_link or navbar_flags.show_tax_brackets_link %}
                                    <li><hr class="dropdown-divider"></li>
                                {% endif %}

                                {% if navbar_flags.show_salary_grades_link or navbar_flags.show_salary_components_link or navbar_flags.show_tax_brackets_link %}
                                    <li><h6 class="dropdown-header">Payroll Setup</h6></li>
                                    
                                    {% if navbar_flags.show_salary_grades_link %}
                                    <li><a class="dropdown-item {% if 'salarygrade' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'hr:salarygrade_list' %}">Salary Grades</a></li>
                                    {% endif %}

                                    {% if navbar_flags.show_salary_components_link %}
                                    <li><a class="dropdown-item {% if 'salarycomponent' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'hr:salarycomponent_list' %}">Salary Components</a></li>
                                    {% endif %}

                                    {% if navbar_flags.show_tax_brackets_link %}
                                    <li><a class="dropdown-item {% if 'taxbracket' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'hr:taxbracket_list' %}">Tax Brackets</a></li>
                                    {% endif %}

                                {% endif %}
                                {# --- END OF PAYROLL SETUP SECTION --- #}
                    </ul>
                </li>
                {% endif %}
                
                
                <!-- Calendar Management -->
                {% if navbar_flags.show_calendar_dropdown %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {% is_active_nav '' 'school_calendar' %}"
                    href="#" id="calendarNavDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-calendar-event me-1"></i>Calendar
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="calendarNavDropdown">
                        
                        {# View Links - visible to most staff #}
                        {% if navbar_flags.show_view_calendar_link %}
                            <li>
                                <a class="dropdown-item {% is_active_nav 'school_calendar:calendar' %}"
                                href="{% url 'school_calendar:calendar' %}">
                                    <i class="bi bi-calendar3 me-2"></i>View Calendar
                                </a>
                            </li>
                        {% endif %}
                        {% if navbar_flags.show_all_events_link %}
                            <li>
                                <a class="dropdown-item {% is_active_nav 'school_calendar:event_list' %}"
                                href="{% url 'school_calendar:event_list' %}">
                                    <i class="bi bi-list-stars me-2"></i>All Events
                                </a>
                            </li>
                        {% endif %}

                        {# Admin Links - visible only to users with the 'manage_all_events' permission #}
                        {% if navbar_flags.show_create_event_link or navbar_flags.show_manage_events_link %}
                            <li><hr class="dropdown-divider"></li>
                            {% if navbar_flags.show_create_event_link %}
                                <li>
                                    <a class="dropdown-item {% is_active_nav 'school_calendar:admin_event_create' %}"
                                    href="{% url 'school_calendar:admin_event_create' %}">
                                        <i class="bi bi-calendar-plus me-2"></i>Create Event
                                    </a>
                                </li>
                            {% endif %}
                            {% if navbar_flags.show_manage_events_link %}
                                <li>
                                    <a class="dropdown-item {% is_active_nav 'school_calendar:admin_event_list' %}"
                                    href="{% url 'school_calendar:admin_event_list' %}">
                                        <i class="bi bi-gear-fill me-2"></i>Manage Events
                                    </a>
                                </li>
                            {% endif %}
                        {% endif %}

                    </ul>
                </li>
                {% endif %}
                    
                    <!-- Finance Dropdown -->
                    {% if navbar_flags.show_finance_dropdown %}
                    <li class="nav-item dropdown">
                        {# The dropdown is active if the current app is 'finance' OR 'accounting' #}
                        <a class="nav-link dropdown-toggle {% is_active_nav '' 'finance,accounting' %}"
                        href="#" id="financeNavDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-wallet2 me-1"></i>Finance
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="financeNavDropdown">
                            
                            {% if navbar_flags.show_expenses_link %}
                                <li><a class="dropdown-item {% is_active_nav 'finance:expense_list' %}" href="{% url 'finance:expense_list' %}">Expenses</a></li>
                            {% endif %}

                            {% if navbar_flags.show_expense_categories_link %}
                                <li>
                                    <a class="dropdown-item {% if 'expense_category' in request.resolver_match.url_name %}active{% endif %}" 
                                    href="{% url 'finance:expense_category_list' %}">
                                        Expense Categories
                                    </a>
                                </li>
                            {% endif %}

                            {% if navbar_flags.show_budgets_link %}
                                <li><a class="dropdown-item {% is_active_nav 'finance:budget_item_list' %}" href="{% url 'finance:budget_item_list' %}">Budgets</a></li>
                            {% endif %}

                            {% if navbar_flags.show_manage_vendors_link %}
                                <li><a class="dropdown-item {% is_active_nav 'finance:vendor_list' %}" href="{% url 'finance:vendor_list' %}">Manage Vendors</a></li>
                            {% endif %}

                            {# Divider if there's a mix of Finance and Accounting items #}
                            {% if navbar_flags.show_expenses_link or navbar_flags.show_budgets_link or navbar_flags.show_manage_vendors_link and navbar_flags.show_manual_journal_entries_link or navbar_flags.show_chart_of_accounts_link or navbar_flags.show_general_ledger_link or navbar_flags.show_account_ledger_link %}
                                <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            
                            {% if navbar_flags.show_chart_of_accounts_link %}
                                <li><a class="dropdown-item {% is_active_nav 'accounting:accounts_list' %}" href="{% url 'accounting:accounts_list' %}">Chart of Accounts</a></li>
                            {% endif %}
                            
                            {# --- ADDED GENERAL LEDGER LINK --- #}
                            {% if navbar_flags.show_general_ledger_link %}
                                <li>
                                    <a class="dropdown-item {% is_active_nav 'accounting:general_ledger' %}" href="{% url 'accounting:general_ledger' %}">
                                        <i class="bi bi-journal-bookmark me-2"></i>{% trans "General Ledger" %}
                                    </a>
                                </li>
                            {% endif %}

                            {# --- ADDED ACCOUNT LEDGER LINK --- #}
                            {% if navbar_flags.show_account_ledger_link %}
                                <li>
                                    <a class="dropdown-item {% is_active_nav 'accounting:account_ledger' %}" href="{% url 'accounting:account_ledger' %}">
                                        <i class="bi bi-journal-text me-2"></i>{% trans "Account Ledger" %}
                                    </a>
                                </li>
                            {% endif %}

                            {% if navbar_flags.show_manual_journal_entries_link %}
                                <li>
                                    <a class="dropdown-item {% is_active_nav 'accounting:journalentry_list' %}" href="{% url 'accounting:journalentry_list' %}">
                                        <i class="bi bi-book-half me-2"></i>{% trans "Manual Journal Entries" %}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </li>
                    {% endif %}


                    <!-- Reports Dropdown -->
                    {% if navbar_flags.show_reports_dropdown %} {# <-- Use the single, high-level flag #}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% is_active_nav '' 'reporting' %}"
                        href="#" id="reportsNavDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-graph-up me-1"></i>Reports
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="reportsNavDropdown">
                            
                            {# Fee & Student Reports #}
                            {% if navbar_flags.show_outstanding_fees_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:outstanding_fees_report' %}" href="{% url 'reporting:outstanding_fees_report' %}">Outstanding Fees</a></li>
                            {% endif %}
                            {% if navbar_flags.show_collection_report_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:collection_report' %}" href="{% url 'reporting:collection_report' %}">Collection Report</a></li>
                            {% endif %}
                            {% if navbar_flags.show_payment_summary_report_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:payment_summary_report' %}" href="{% url 'reporting:payment_summary_report' %}">Payment Summary</a></li>
                            {% endif %}
                            {% if navbar_flags.show_student_ledger_report_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:student_ledger_report' %}" href="{% url 'reporting:student_ledger_report' %}">Student Ledger</a></li>
                            {% endif %}

                            {# Divider if there's a mix #}
                            {% if navbar_flags.show_student_ledger_report_link or navbar_flags.show_outstanding_fees_link and navbar_flags.show_trial_balance_link or navbar_flags.show_expense_report_link %}
                                <li><hr class="dropdown-divider"></li>
                            {% endif %}

                            {# Financial & Accounting Reports #}
                            {% if navbar_flags.show_expense_report_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:expense_report' %}" href="{% url 'reporting:expense_report' %}">Expense Report</a></li>
                            {% endif %}
                            {% if navbar_flags.show_trial_balance_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:trial_balance_report' %}" href="{% url 'reporting:trial_balance_report' %}">Trial Balance</a></li>
                            {% endif %}
                            {% if navbar_flags.show_income_statement_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:income_expense_report' %}" href="{% url 'reporting:income_expense_report' %}">Income Statement</a></li>
                            {% endif %}
                            {% if navbar_flags.show_balance_sheet_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:balance_sheet_report' %}" href="{% url 'reporting:balance_sheet_report' %}">Balance Sheet</a></li>
                            {% endif %}
                            {% if navbar_flags.show_cash_flow_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:cash_flow_statement_report' %}" href="{% url 'reporting:cash_flow_statement_report' %}">Cash Flow</a></li>
                            {% endif %}

                            {# Divider if there's a mix #}
                            {% if navbar_flags.show_balance_sheet_link and navbar_flags.show_budget_variance_report_link %}
                                <li><hr class="dropdown-divider"></li>
                            {% endif %}

                            {# Budget & Projection Reports #}
                            {% if navbar_flags.show_budget_variance_report_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:budget_variance_report' %}" href="{% url 'reporting:budget_variance_report' %}">Budget Variance</a></li>
                            {% endif %}

                            {% if navbar_flags.show_fee_projection_report_link %}
                                <li><a class="dropdown-item {% is_active_nav 'reporting:fee_projection_report' %}" href="{% url 'reporting:fee_projection_report' %}">Fee Projection</a></li>
                            {% endif %}
                            
                            {% if navbar_flags.show_payroll_summary_report_link %}
                                <li><a class="dropdown-item {% is_active_nav 'hr:payroll_summary_report' %}" href="{% url 'hr:payroll_summary_report' %}">Payroll Summary</a></li>
                            {% endif %}

                        </ul>
                    </li>
                    {% endif %}

                    
                <!-- Setup & Admin Dropdown -->
                {% if navbar_flags.show_setup_admin_dropdown %}
                <li class="nav-item dropdown">
                    {# This complex active check covers all pages within this dropdown #}
                    <a class="nav-link dropdown-toggle 
                    {% is_active_nav 'schools:profile_update,schools:class_list,schools:academic_year_list,schools:term_list,fees:fee_head_list,fees:fee_structure_list,payments:payment_method_list,finance:vendor_list,portal_admin:group_list,portal_admin:assign_staff_roles,portal_admin:activity_log_list' 'announcements' %}"
                    href="#" id="setupAdminNavDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-sliders me-1"></i>Setup & Admin
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="setupAdminNavDropdown">
                        
                        {# --- School & Academic Setup Section --- #}
                        <li><h6 class="dropdown-header">School & Academic Setup</h6></li>
                        {% if navbar_flags.show_school_profile_link %}
                            <li><a class="dropdown-item {% is_active_nav 'schools:profile_update' %}" href="{% url 'schools:profile_update' %}">School Profile</a></li>
                        {% endif %}
                        {% if navbar_flags.show_manage_classes_link %}
                            <li><a class="dropdown-item {% is_active_nav 'schools:class_list' %}" href="{% url 'schools:class_list' %}">Classes & Sections</a></li>
                        {% endif %}
                        {% if navbar_flags.show_manage_academic_years_link %}
                            <li><a class="dropdown-item {% is_active_nav 'schools:academic_year_list' %}" href="{% url 'schools:academic_year_list' %}">Academic Years</a></li>
                        {% endif %}
                        {% if navbar_flags.show_manage_terms_link %}
                            <li><a class="dropdown-item {% is_active_nav 'schools:term_list' %}" href="{% url 'schools:term_list' %}">Terms</a></li>
                        {% endif %}
                        {% if navbar_flags.show_announcements_link %}
                            <li><a class="dropdown-item {% is_active_nav '' 'announcements' %}" href="{% url 'announcements:announcement_list' %}">Announcements</a></li>
                        {% endif %}

                        {# --- Financial Setup Section --- #}
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Financial Setup</h6></li>
                        {% if navbar_flags.show_manage_fee_heads_link %}
                            <li><a class="dropdown-item {% is_active_nav 'fees:fee_head_list' %}" href="{% url 'fees:fee_head_list' %}">Fee Heads</a></li>
                        {% endif %}
                        {% if navbar_flags.show_manage_fee_structures_link %}
                            <li><a class="dropdown-item {% is_active_nav 'fees:fee_structure_list' %}" href="{% url 'fees:fee_structure_list' %}">Fee Structures</a></li>
                        {% endif %}
                        {% if navbar_flags.show_manage_payment_methods_link %}
                            <li><a class="dropdown-item {% is_active_nav 'payments:payment_method_list' %}" href="{% url 'payments:payment_method_list' %}">Payment Methods</a></li>
                        {% endif %}
                        
                        {# --- ADDED VENDOR MANAGEMENT LINK --- #}
                        {% if navbar_flags.show_manage_vendors_link %}
                            <li>
                                <a class="dropdown-item {% is_active_nav 'finance:vendor_list' %}" 
                                href="{% url 'finance:vendor_list' %}">
                                    Manage Vendors
                                </a>
                            </li>
                        {% endif %}
                        {# --- END OF VENDOR LINK --- #}

                        {# --- Tenant Administration Section --- #}
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Tenant Administration</h6></li>
                        {% if navbar_flags.show_manage_roles_link %}
                            <li><a class="dropdown-item {% is_active_nav 'portal_admin:group_list' %}" href="{% url 'portal_admin:group_list' %}">Manage Roles & Permissions</a></li>
                        {% endif %}
                        {% if navbar_flags.show_assign_staff_to_roles_link %}
                            <li><a class="dropdown-item {% is_active_nav 'portal_admin:assign_staff_roles' %}" href="{% url 'portal_admin:assign_staff_roles' %}">Assign Staff to Roles</a></li>
                        {% endif %}
                        {% if navbar_flags.show_activity_log_link %}
                            <li><a class="dropdown-item {% is_active_nav 'portal_admin:activity_log_list' %}" href="{% url 'portal_admin:activity_log_list' %}">Activity Log</a></li>
                        {% endif %}

                    </ul>
                </li>
                {% endif %}
                
                
                {# --- PARENT USER MAIN NAVIGATION --- #}
                {% if user_type_flags.IS_TENANT_PARENT_USER and tenant_features.PARENT_PORTAL %}
                    {% if navbar_flags.show_parent_dashboard_link %}
                        <li class="nav-item">
                            <a class="nav-link {% if current_view_name == 'parent_portal:dashboard' %}active{% endif %}" href="{% url 'parent_portal:dashboard' %}">
                                <i class="bi bi-layout-text-sidebar-reverse me-1"></i>My Dashboard
                            </a>
                        </li>
                    {% endif %}


                    {% if navbar_flags.show_parent_children_fees_link %}
                        {# <li class="nav-item">
                            <a class="nav-link {% if current_view_name == 'parent_portal:children_fees_summary' %}active{% endif %}" href="{% url 'parent_portal:children_fees_summary' %}"><i class="bi bi-credit-card-2-front-fill me-1"></i>My Children's Fees</a>
                        </li> #}
                        {# OR, if you want a placeholder link that doesn't error: #}
                        <li class="nav-item">
                            <a class="nav-link disabled" href="#" title="Coming Soon"><i class="bi bi-credit-card-2-front-fill me-1"></i>My Children's Fees</a>
                        </li>
                    {% endif %}


                {% endif %}
            </ul>
            {% comment %} {% endwith %} {# End WITH_RESOLVER_VARS #} {% endcomment %}

            {# === RIGHT-ALIGNED ITEMS (User profile dropdown, login links) === #}
            <div class="d-flex align-items-center ms-auto">
                {% if user_type_flags.IS_TENANT_STAFF_USER and global_academic_year_filter_form %}
                <form method="post" action="{% url 'common_filters:set_global_academic_year' %}" class="d-flex global-filter-form me-3 my-2 my-lg-0">
                    {% csrf_token %}
                    {{ global_academic_year_filter_form.academic_year }}
                    <button type="submit" class="btn btn-sm btn-outline-secondary ms-2" style="display:none;" aria-label="Apply Year Filter">Go</button>
                </form>
                {% endif %}
                <ul class="navbar-nav mb-2 mb-lg-0">
                    {% if user.is_authenticated and request.tenant %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="currentUserDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-1"></i>

                                {% if user_for_tenant_perms %}
                                    {{ user_for_tenant_perms.get_full_name|default:user_for_tenant_perms.email|default:user.email }}
                                {% else %}
                                    {{ user.get_full_name|default:user.email|default:"User" }}
                                {% endif %}

                                {% comment %} {{ user_for_tenant_perms.get_full_name|default:user_for_tenant_perms.email|default:user.email }} {% endcomment %}
                                {% if user_type_flags.IS_TENANT_STAFF_USER %}
                                    <span class="badge bg-info ms-1" style="font-size: 0.7em;">Staff</span>
                                {% elif user_type_flags.IS_TENANT_PARENT_USER and tenant_features.PARENT_PORTAL %}
                                    <span class="badge bg-success ms-1" style="font-size: 0.7em;">Parent</span>
                                {% elif user_type_flags.IS_PUBLIC_ADMIN_USER %}
                                    <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7em;">Owner</span>
                                {% endif %}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="currentUserDropdown">

                                {# <<< --- ADD YOUR NEW LINK HERE, for example --- >>> #}
                                {% if request.user.is_authenticated %} {# Simplified condition for now, adjust as needed #}
                                    <li><a class="dropdown-item" href="{% url 'hr:my_leave_summary' %}"><i class="bi bi-card-list me-2"></i>My Leave Summary</a></li>
                                {% endif %}
                                {# <<< --- END OF YOUR NEW LINK --- >>> #}

                                {% if user_type_flags.IS_TENANT_STAFF_USER %}
                                <li>
                                    <a class="dropdown-item" href="{% url 'schools:staff_detail' pk=request.user.pk %}">My Staff Profile</a>
                                    <a class="dropdown-item" href="{% url 'hr:my_payslip_list' %}">My Payslips</a>
                                </li>
                                    {% if navbar_flags.show_school_profile_link %}
                                        <li><a class="dropdown-item" href="{% url 'schools:profile_update' %}">School Settings</a></li>
                                    {% endif %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'schools:staff_logout_view' %}"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>

                                {% elif user_type_flags.IS_TENANT_PARENT_USER and tenant_features.PARENT_PORTAL %}
                                    <li><a class="dropdown-item {% if current_view_name == 'parent_portal:profile_display' or current_view_name == 'parent_portal:profile_update' %}active{% endif %}" href="{% url 'parent_portal:profile_display' %}"><i class="bi bi-person-badge me-2"></i>My Profile</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'schools:parent_logout' %}"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                                {% elif user_type_flags.IS_PUBLIC_ADMIN_USER %}
                                    <li><span class="dropdown-item-text text-muted small">Platform Admin</span></li>
                                    {% if request.tenant and request.tenant.owner == user %}
                                    <li><a class="dropdown-item" href="{% url 'schools:dashboard' %}"><i class="bi bi-speedometer2 me-2"></i>Tenant Dashboard</a></li>
                                    {% endif %}
                                    <li><a class="dropdown-item" href="{% url 'public_site:home' %}">Back to Platform</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'users:school_admin_logout' %}"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                                {% endif %}
                            </ul>
                        </li>
                    {% elif request.tenant %}
                        <li class="nav-item">
                            {% if current_app_name == 'parent_portal' or 'parents' in request.path %}
                                <a class="nav-link" href="{% url 'schools:parent_login' %}"><i class="bi bi-box-arrow-in-right me-1"></i>Parent Login</a>
                            {% else %}
                                <a class="nav-link" href="{% url 'schools:staff_login' %}"><i class="bi bi-box-arrow-in-right me-1"></i>Staff Login</a>
                            {% endif %}
                        </li>
                    {% endif %}
                </ul>
            </div> {# End RIGHT-ALIGNED d-flex ms-auto #}
            {% comment %} {% endwith %} {# End WITH_RESOLVER_VARS #}  {% endcomment %}
        </div> {# End .collapse #}
    </div> {# End .container-fluid #}
</nav>

{% endblock navbar %}

{% block content %}
    <div class="container-fluid mt-3 mb-5 py-3 main-content-area">
        {% if messages %}
            <div class="container">
                {% include "partials/_messages.html" %}
            </div>
        {% endif %}

        {% block tenant_specific_content %}
            {% if request.tenant %}
            <div class="container">
                {% if user_type_flags.IS_TENANT_STAFF_USER %}
                    <h1 class="display-6">Welcome to {{ request.tenant.name }} Staff Portal</h1>
                    <p class="lead">Use the navigation menu to manage school operations.</p>
                    {% if selected_global_academic_year_obj %} {# Use selected_global_academic_year_obj passed from context #}
                        <div class="alert alert-info mt-3" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            Currently viewing data for Academic Year: <strong>{{ selected_global_academic_year_obj.name }}</strong>
                            <form id="clearGlobalYearForm" method="post" action="{% url 'common_filters:set_global_academic_year' %}" class="d-inline ms-2">
                                {% csrf_token %}
                                <input type="hidden" name="academic_year" value="">
                                <button type="submit" class="btn btn-sm btn-outline-info alert-link p-0 border-0">(Clear Filter)</button>
                            </form>
                        </div>
                    {% endif %}
                {% elif user_type_flags.IS_TENANT_PARENT_USER and tenant_features.PARENT_PORTAL %}
                    <h1 class="display-6">Welcome to {{ request.tenant.name }} Parent Portal</h1>
                    <p class="lead">Access your child's information and fee details.</p>
                {% else %}
                    <h1 class="display-6">Welcome to {{ request.tenant.name }} Portal</h1>
                {% endif %}

                <!-- DEBUG INFO BLOCK -->
                {% if debug %}
                    <div class="container">
                        <div class="debug-info-block">
                            {# <!-- DEBUG INFO BLOCK FROM TENANT_BASE.HTML - FILE V9 (Your Comment) --> #}
                            <h6 class="text-muted">Debug Information (rendered by tenant_base.html):</h6>
                            <p><strong>Value of 'debug' context variable: {{ debug }}</strong></p>
                            <p><strong>User:</strong> {{ request.user.email|default:request.user|default:"Anonymous" }} | <strong>Auth:</strong> {{ request.user.is_authenticated }}</p>
                            <p><strong>Effective Tenant User (for perms):</strong> {{ user_for_tenant_perms.email|default:"None" }}</p>
                            <p><strong>Tenant:</strong> {{ request.tenant.name|default:"N/A" }} (Schema: {{ request.tenant.schema_name|default:"N/A" }})</p>
                            <p><strong>User Type Flags (CP):</strong> {{ user_type_flags|default:"Not Set" }}</p>
                            <p><strong>Active Tenant Features (CP):</strong> {{ tenant_features|default:"Not Set" }}</p>
                            <p><strong>Selected Global Academic Year (from session obj):</strong> {{ selected_global_academic_year_obj.name|default:"None Selected" }} (PK: {{ selected_global_academic_year_obj.pk|default:"N/A" }})</p>
                            <h6>All Navbar Flags (from Context Processor):</h6>
                            <ul>
                                {% for flag_name, flag_value in navbar_flags_sorted_for_debug.items %}
                                    <li>{{ flag_name }}: <strong class="{% if flag_value %}perm-true{% else %}perm-false{% endif %}">{{ flag_value }}</strong></li>
                                {% endfor %}
                            </ul>
                            {% if user_type_flags.IS_TENANT_STAFF_USER and perms %}
                                <h6>Staff Permission Checks (Template Level - using 'perms' object for `user_for_tenant_perms`):</h6>
                                <table class="debug-permissions-table">
                                    <thead><tr><th>Permission String</th><th>Evaluates To</th></tr></thead>
                                    <tbody>
                                        <tr><td>perms.accounting.add_account</td><td class="{% if perms.accounting.add_account %}perm-true{% else %}perm-false{% endif %}">{{ perms.accounting.add_account|yesno:"True,False" }}</td></tr>
                                        <tr><td>perms.fees.view_invoice</td><td class="{% if perms.fees.view_invoice %}perm-true{% else %}perm-false{% endif %}">{{ perms.fees.view_invoice|yesno:"True,False" }}</td></tr>
                                        <tr><td>perms.accounting.view_chartofaccount</td><td class="{% if perms.accounting.view_chartofaccount %}perm-true{% else %}perm-false{% endif %}">{{ perms.accounting.view_chartofaccount|yesno:"True,False" }}</td></tr>
                                        <tr><td>perms.schools.view_academicyear</td><td class="{% if perms.schools.view_academicyear %}perm-true{% else %}perm-false{% endif %}">{{ perms.schools.view_academicyear|yesno:"True,False" }}</td></tr>
                                        <tr><td>perms.schools.view_term</td><td class="{% if perms.schools.view_term %}perm-true{% else %}perm-false{% endif %}">{{ perms.schools.view_term|yesno:"True,False" }}</td></tr>
                                    </tbody>
                                </table>
                            {% endif %}
                        </div>
                    </div>
                {% else %}
                    <div class="container">
                        <p class="alert alert-danger">DEBUG BLOCK NOT RENDERED: The 'debug' context variable was False or not provided.</p>
                    </div>
                {% endif %} <!-- END DEBUG BLOCK IF -->

                {% if latest_platform_announcement %}
                <div class="container-fluid p-0"> {# Full width banner #}
                    <div class="alert alert-info platform-announcement-banner mb-0 rounded-0 border-start-0 border-end-0" role="alert"
                        style="background-color: #e6f7ff; border-color: #91d5ff; color: #0050b3;"> {# Example light blue styling #}
                        <div class="container"> {# Constrain text width #}
                            <div class="d-flex align-items-center">
                                <i class="bi bi-megaphone-fill fs-4 me-3"></i>
                                <div>
                                    <strong>{{ latest_platform_announcement.title }}</strong><br>
                                    {{ latest_platform_announcement.content|striptags|truncatewords:25 }}
                                    {# For a full detail page, you'd need a public-facing detail view for announcements #}
                                    {# For now, we just show a snippet. If you want a "Read More", you'll need to build that. #}
                                    {# <a href="#" class="alert-link ms-2 fw-bold">Read More</a> #}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

            </div>
            {% else %}
            <div class="container">
                <p class="alert alert-warning">Tenant context not found. This page may not function as expected.</p>
            </div>
            {% endif %}
        {% endblock tenant_specific_content %}
    </div>
{% endblock content %}

{% block footer %}
<footer class="footer mt-auto py-3 bg-light border-top text-center fixed-footer">
    <div class="container">
        <span class="text-muted">© {% now "Y" %} {{ request.tenant.name|default:"Your School" }}. Fees Management Portal.</span>
    </div>
</footer>
{% endblock footer %}

{% block page_specific_js %}
    {{ block.super }}
    {% if user_type_flags.IS_TENANT_STAFF_USER and global_academic_year_filter_form %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const academicYearSelect = document.querySelector('.global-filter-form select[name="academic_year"]');
            if (academicYearSelect) {
                const form = academicYearSelect.closest('form'); // Get the parent form
                if (form) { // Ensure form exists
                    academicYearSelect.addEventListener('change', function() {
                        form.submit();
                    });
                }
            }
        });
    </script>
    {% endif %}
{% endblock page_specific_js %}

