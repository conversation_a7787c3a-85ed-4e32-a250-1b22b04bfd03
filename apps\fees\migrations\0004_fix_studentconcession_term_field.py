# Generated manually to fix missing term_id column in StudentConcession table

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fees', '0003_initial'),
        ('schools', '0001_initial'),
    ]

    operations = [
        # Check if the term field exists, and add it if it doesn't
        migrations.RunSQL(
            sql=[
                """
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'fees_studentconcession' 
                        AND column_name = 'term_id'
                        AND table_schema = current_schema()
                    ) THEN
                        ALTER TABLE fees_studentconcession 
                        ADD COLUMN term_id bigint;
                        
                        ALTER TABLE fees_studentconcession 
                        ADD CONSTRAINT fees_studentconcession_term_id_fkey 
                        FOREIGN KEY (term_id) REFERENCES schools_term(id) 
                        DEFERRABLE INITIALLY DEFERRED;
                        
                        CREATE INDEX fees_studentconcession_term_id_idx 
                        ON fees_studentconcession(term_id);
                    END IF;
                END $$;
                """
            ],
            reverse_sql=[
                """
                DO $$
                BEGIN
                    IF EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'fees_studentconcession' 
                        AND column_name = 'term_id'
                        AND table_schema = current_schema()
                    ) THEN
                        ALTER TABLE fees_studentconcession DROP COLUMN term_id;
                    END IF;
                END $$;
                """
            ]
        ),
    ]
