{# templates/hr/my_leave_summary.html #}
{% extends "tenant_base.html" %}
{% load humanize %}

{% block title %}My Leave Summary{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">
    <h1>My Leave Summary</h1>
    <p class="lead">User: {{ request.user.full_name }}</p>

    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">My Leave Balances</h5>
        </div>
        <div class="card-body">
            {% if leave_balances %}
                <table class="table table-sm table-hover">
                    {# In the table for leave balances #}
                    <thead class="table-light">
                        <tr>
                            <th>Leave Type</th>
                            <th class="text-center">Entitled</th>
                            <th class="text-center">Taken</th>
                            <th class="text-center">Available</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for balance in leave_balances %}
                            <tr>
                                <td><strong>{{ balance.leave_type.name }}</strong></td>
                                <td class="text-center">{{ balance.days_accrued }}</td>
                                <td class="text-center">{{ balance.days_taken }}</td> {# <-- This now calls the @property #}
                                <td class="text-center fw-bold">{{ balance.days_available }}</td> {# <-- This calls the correct @property #}
                            </tr>
                        {% empty %}
                            <tr><td colspan="4" class="text-center p-3">No leave balances found.</td></tr>
                        {% endfor %}
                    </tbody>
                    {% comment %} <thead>
                        <tr>
                            <th>Leave Type</th>
                            <th class="text-end">Accrued/Entitled</th>
                            <th class="text-end">Taken</th>
                            <th class="text-end">Available Balance</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for balance in leave_balances %}
                        <tr>
                            <td>{{ balance.leave_type.name }}</td>
                            <td class="text-end">{{ balance.days_accrued|floatformat:1|default:"N/A" }}</td>
                            <td class="text-end">{{ balance.days_taken|floatformat:1|default:"0.0" }}</td>
                            <td class="text-end fw-bold">{{ balance.days_remaining|floatformat:1|default:"N/A" }}</td>
                        </tr>
                        {% endfor %}
                    </tbody> {% endcomment %}
                </table>
            {% else %}
                <p class="text-muted">No leave balances found for your profile.</p>
            {% endif %}
        </div>
    </div>

    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">My Recent Leave Requests</h3>
        <a href="{% url 'hr:staff_leaverequest_apply' %}" class="btn btn-primary btn-sm">
            <i class="bi bi-plus-circle-fill me-1"></i> Apply for New Leave
        </a>
    </div>

    {% if recent_requests %}
        <div class="table-responsive">
            <table class="table table-striped table-hover table-sm">
                <thead class="table-light">
                    <tr>
                        <th>Type</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th>Days</th>
                        <th>Status</th>
                        <th>Requested On</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for req in recent_requests %}
                    <tr>
                        <td>{{ req.leave_type.name }}</td>
                        <td>{{ req.start_date|date:"d M Y" }}</td>
                        <td>{{ req.end_date|date:"d M Y" }}</td>
                        <td class="text-center">{{ req.duration|floatformat:1 }}</td>
                        <td>{% include "hr/partials/_leave_status_badge.html" with status=req.status %}</td>
                        <td>{{ req.created_at|date:"d M Y" }}</td>
                        <td>
                            <a href="{% url 'hr:staff_leaverequest_detail' req.pk %}" class="btn btn-info btn-sm">View</a>
                            {% if req.status == 'PENDING' %}
                                {# Add cancel button/form here later #}
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {# Add pagination if leave_requests is paginated #}
    {% else %}
        <p>You have not submitted any leave requests yet.</p>
    {% endif %}
    <a href="{% url 'schools:dashboard' %}" class="btn btn-secondary mt-3">Back to Dashboard</a>
</div>
{% endblock %}

