{# D:\school_fees_saas_v2\apps\hr\templates\hr\staff_leaverequest_detail.html #}
{% extends "tenant_base.html" %}
{% load static i18n humanize string_utils %}

{% block tenant_page_title %}{{ view_title }}{% endblock %}

{% block tenant_specific_content %}
<div class="container mt-4">

    {# Page Header with Breadcrumbs #}
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">{{ view_title }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'hr:staff_dashboard' %}">{% trans "My HR Dashboard" %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'hr:staff_leaverequest_list' %}">{% trans "My Leave Requests" %}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{% trans "Request Details" %}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{% url 'hr:staff_leaverequest_list' %}" class="btn btn-secondary btn-sm">
                <i class="bi bi-arrow-left-circle me-1"></i> {% trans "Back to List" %}
            </a>
        </div>
    </div>
    
    {% include "partials/_messages.html" %}

    <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{% trans "Leave Request for" %} {{ leave_request.start_date|date:"F" }}</h5>
            {# Use your model method to get a nice badge class #}
            <span class="badge fs-6 bg-{{ leave_request.get_status_badge_class }}">
                {{ leave_request.get_status_display }}
            </span>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>{% trans "Employee" %}:</strong> {{ leave_request.employee.user.get_full_name }}</p>
                    <p><strong>{% trans "Leave Type" %}:</strong> {{ leave_request.leave_type.name }}</p>
                    <p><strong>{% trans "Requested on" %}:</strong> {{ leave_request.request_date|date:"d M Y, H:i" }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>{% trans "Start Date" %}:</strong> {{ leave_request.start_date|date:"l, d F Y" }}</p>
                    <p><strong>{% trans "End Date" %}:</strong> {{ leave_request.end_date|date:"l, d F Y" }}</p>
                    <p><strong>{% trans "Total Days Requested" %}:</strong> {{ leave_request.number_of_days_requested_calc }}</p>
                </div>
            </div>
            
            <hr>

            <div>
                <h5>{% trans "Reason" %}</h5>
                <p class="p-3 bg-light rounded">{{ leave_request.reason|linebreaksbr }}</p>
            </div>

            {% if leave_request.attachment %}
                <div class="mt-3">
                    <strong>{% trans "Attachment" %}:</strong>
                    <a href="{{ leave_request.attachment.url }}" target="_blank">
                        <i class="bi bi-paperclip"></i> {{ leave_request.attachment.name|split:"/"|last }}
                    </a>
                </div>
            {% endif %}

            {% if leave_request.status == 'REJECTED' or leave_request.status == 'APPROVED' %}
                <hr class="my-4">
                <div class="p-3 rounded" style="background-color: #f8f9fa; border-left: 4px solid #6c757d;">
                    <h5 class="mb-3">{% trans "Manager's Response" %}</h5>
                    <p><strong>{% trans "Action Taken By" %}:</strong> {{ leave_request.approved_by.user.get_full_name|default:"N/A" }}</p>
                    <p><strong>{% trans "Date of Action" %}:</strong> {{ leave_request.approval_date|date:"l, d F Y" }}</p>
                    {% if leave_request.admin_notes %}
                        <p class="mt-2"><strong>{% trans "Notes" %}:</strong></p>
                        <p class="mb-0"><em>{{ leave_request.admin_notes|linebreaksbr }}</em></p>
                    {% endif %}
                </div>
            {% endif %}

        </div>
        <div class="card-footer text-end">
            {# A staff member can only cancel a PENDING request #}
            {% if leave_request.status == 'PENDING' %}
                <a href="#" class="btn btn-danger disabled" title="Cancel Request (Coming Soon)">
                    <i class="bi bi-x-octagon-fill me-1"></i> {% trans "Cancel My Request" %}
                </a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}


