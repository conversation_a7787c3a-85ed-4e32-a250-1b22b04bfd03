# D:\school_fees_saas_v2\apps\hr\forms.py
from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm # For StaffUser creation
from django.db import transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _ # <<< --- ADD THIS IMPORT ---
from django.core.exceptions import ValidationError

# Import models
from apps.schools.models import StaffUser # StaffUser is in schools app
from .models import (
    EmployeeProfile, GENDER_CHOICES, EMPLOYMENT_TYPE_CHOICES, MARITAL_STATUS_CHOICES,
    LeaveType, LeaveRequest, LeaveBalance, LEAVE_REQUEST_STATUS_CHOICES
)


from .models import (
    LeaveType, LeaveRequest, LeaveBalance,
    SalaryComponent, StaffSalaryStructure, SalaryStructureComponent, # The new, correct models
    PayrollRun, Payslip
)


# ========================================
# FORM 1: Creating a NEW Staff Member (StaffUser + EmployeeProfile)
# This form is used by views in apps.schools
# ========================================

# apps/hr/forms.py
from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.db import transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from .models import LeaveType, LeaveRequest, EmployeeProfile, LeaveBalance 

# Models
from apps.schools.models import StaffUser # User model with core HR fields
from .models import EmployeeProfile, GENDER_CHOICES, EMPLOYMENT_TYPE_CHOICES, MARITAL_STATUS_CHOICES # Profile model & choices


# ... (your GENDER_CHOICES etc. definitions) ...
GENDER_CHOICES = [('', '---------'), ('M', 'Male'), ('F', 'Female'), ('O', 'Other')] # Example
MARITAL_STATUS_CHOICES = [('', '---------'), ('S', 'Single'), ('M', 'Married')] # Example
EMPLOYMENT_TYPE_CHOICES = [('', '---------'), ('FT', 'Full-Time'), ('PT', 'Part-Time')] # Example



from django import forms
from django.db import transaction
from apps.schools.models import StaffUser
from .models import EmployeeProfile

class StaffAndProfileForm(forms.ModelForm):
    """
    A single form to create and update BOTH StaffUser and EmployeeProfile.
    """
    # --- Fields from StaffUser ---
    email = forms.EmailField(label=_("Login Email"), required=True, widget=forms.EmailInput(attrs={'class': 'form-control'}))
    first_name = forms.CharField(label=_("First Name"), max_length=150, required=True, widget=forms.TextInput(attrs={'class': 'form-control'}))
    last_name = forms.CharField(label=_("Last Name"), max_length=150, required=True, widget=forms.TextInput(attrs={'class': 'form-control'}))
    is_active = forms.BooleanField(label=_("Login Account is Active"), required=False, initial=True)
    is_superuser = forms.BooleanField(label=_("Is Tenant Superuser (Full Permissions)"), required=False)

    # --- Password fields for CREATION only ---
    password = forms.CharField(label=_("Password"), widget=forms.PasswordInput(attrs={'class': 'form-control'}), required=False, help_text="Leave blank to keep the current password when editing.")
    
    class Meta:
        model = EmployeeProfile # The form is based on the profile
        # List all fields from EmployeeProfile you want on the form
        fields = [
            'employee_id', 
            'designation', 
            'department', 
            'date_hired', 
            'phone_number_primary', 
            'address_line1', 
            'address_line2', 
            'city',
            'state_province',
            'postal_code',
            'country',
            'photo',
        ]
        widgets = {
            # Add bootstrap classes to the profile fields
            'employee_id': forms.TextInput(attrs={'class': 'form-control'}),
            'designation': forms.TextInput(attrs={'class': 'form-control'}),
            'department': forms.TextInput(attrs={'class': 'form-control'}),
            'date_hired': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'phone_number_primary': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line1': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line2': forms.TextInput(attrs={'class': 'form-control'}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'state_province': forms.TextInput(attrs={'class': 'form-control'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-control'}),
            'country': forms.TextInput(attrs={'class': 'form-control'}),
            # 'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'photo': forms.ClearableFileInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # self.instance is the EmployeeProfile instance (if updating)
        user = self.instance.user if self.instance and self.instance.pk else None
        
        if user:
            # Populate form with data from the User model when editing
            self.fields['email'].initial = user.email
            self.fields['first_name'].initial = user.first_name
            self.fields['last_name'].initial = user.last_name
            self.fields['is_active'].initial = user.is_active
            self.fields['is_superuser'].initial = user.is_superuser
            # Make email read-only during an update
            self.fields['email'].widget.attrs['readonly'] = True
        else:
            # This is a create form, password is required
            self.fields['password'].required = True

    def clean_email(self):
        email = self.cleaned_data.get('email').lower()
        # Check for uniqueness only when creating a new user (when instance has no pk)
        if not self.instance or not self.instance.pk:
            if StaffUser.objects.filter(email__iexact=email).exists():
                raise forms.ValidationError("A staff member with this email already exists.")
        return email

    @transaction.atomic
    def save(self, commit=True):
        # Determine if we are creating a new user or updating an existing one
        is_new_user = not (self.instance and self.instance.pk)
        
        if is_new_user:
            # Create the StaffUser first
            user = StaffUser.objects.create_user(
                email=self.cleaned_data['email'],
                password=self.cleaned_data['password']
            )
            self.instance.user = user # Link the new user to the profile
        else:
            user = self.instance.user

        # Update StaffUser fields from the form
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.is_active = self.cleaned_data['is_active']
        user.is_superuser = self.cleaned_data['is_superuser']
        
        # Update password only if a new one was provided
        new_password = self.cleaned_data.get('password')
        if new_password:
            user.set_password(new_password)
        
        # Save the StaffUser instance
        user.save()
        
        # Now, save the EmployeeProfile instance.
        # super().save() will update the profile fields from cleaned_data
        profile = super().save(commit)
        return profile
    

# class StaffAndProfileCreationForm(UserCreationForm):
#     # --- Fields directly on StaffUser model (beyond UserCreationForm defaults) ---
#     # UserCreationForm handles 'email' (as USERNAME_FIELD) and password fields.
#     # We re-declare first_name and last_name to customize widgets/labels if needed,
#     # or they can be omitted here if UserCreationForm's defaults are fine.
#     first_name = forms.CharField(
#         label=_("First Name"), max_length=150, required=True,
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm', 'placeholder': 'Enter first name'})
#     )
#     last_name = forms.CharField(
#         label=_("Last Name"), max_length=150, required=True,
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm', 'placeholder': 'Enter last name'})
#     )
#     employee_id = forms.CharField(
#         label=_("Employee ID"), max_length=50, required=True, # Consider if truly required or can be auto-generated/optional
#         help_text=_("School-specific unique employee ID."),
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm'})
#     )
#     designation = forms.CharField(
#         label=_("Designation/Job Title"), max_length=100, required=True,
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm'})
#     )
#     department = forms.CharField( # This field is on StaffUser as per your save method
#         label=_("Department"), max_length=100, required=False,
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm'})
#     )
#     date_hired = forms.DateField( # This field is on StaffUser
#         label=_("Date Hired"),
#         widget=forms.DateInput(attrs={'type':'date', 'class':'form-control form-control-sm'}),
#         required=True, initial=timezone.now().date()
#     )
#     phone_number_staff = forms.CharField( # This field is on StaffUser (maps to user.phone_number)
#         label=_("Staff Primary Phone"), max_length=30, required=False,
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm'})
#     )
#     is_staff_access = forms.BooleanField( # This field maps to StaffUser.is_staff
#         label=_("Grant Django Admin Access?"), required=False, initial=False,
#         help_text=_("Allows login to this school's Django admin interface. (Sets StaffUser.is_staff)"),
#         widget=forms.CheckboxInput(attrs={'class':'form-check-input'})
#     )

#     # --- Fields for the linked EmployeeProfile model ---
#     # Prefix with 'profile_' to avoid clashes and for clarity in save method
#     profile_middle_name = forms.CharField(label=_("Middle Name(s)"), max_length=100, required=False, widget=forms.TextInput(attrs={'class':'form-control form-control-sm'}))
#     profile_gender = forms.ChoiceField(label=_("Gender"), choices=GENDER_CHOICES, required=False, widget=forms.Select(attrs={'class': 'form-select form-select-sm'}))
#     profile_date_of_birth = forms.DateField(label=_("Date of Birth"), widget=forms.DateInput(attrs={'type':'date', 'class':'form-control form-control-sm'}), required=False)
#     profile_marital_status = forms.ChoiceField(label=_("Marital Status"), choices=MARITAL_STATUS_CHOICES, required=False, widget=forms.Select(attrs={'class': 'form-select form-select-sm'}))
#     profile_phone_number_alternate = forms.CharField(label=_("Alternate Phone (Profile)"), max_length=30, required=False, widget=forms.TextInput(attrs={'class':'form-control form-control-sm'}))
#     profile_address_line1 = forms.CharField(label=_("Address Line 1 (Profile)"),max_length=255, required=False, widget=forms.TextInput(attrs={'class':'form-control form-control-sm'}))
#     profile_address_line2 = forms.CharField(label=_("Address Line 2 (Profile)"),max_length=255, required=False, widget=forms.TextInput(attrs={'class':'form-control form-control-sm'}))
#     profile_city = forms.CharField(label=_("City (Profile)"),max_length=100, required=False, widget=forms.TextInput(attrs={'class':'form-control form-control-sm'}))
#     profile_state_province = forms.CharField(label=_("State/Province (Profile)"), max_length=100, required=False, widget=forms.TextInput(attrs={'class':'form-control form-control-sm'}))
#     profile_postal_code = forms.CharField(label=_("Postal/Zip Code (Profile)"), max_length=20, required=False, widget=forms.TextInput(attrs={'class':'form-control form-control-sm'}))
#     profile_country = forms.CharField(label=_("Country (Profile)"),max_length=100, required=False, widget=forms.TextInput(attrs={'class':'form-control form-control-sm'}))
#     profile_employment_type = forms.ChoiceField(label=_("Employment Type (Profile)"), choices=EMPLOYMENT_TYPE_CHOICES, required=False, widget=forms.Select(attrs={'class': 'form-select form-select-sm'}))
#     profile_photo = forms.ImageField(label=_("Profile Photo"), required=False, widget=forms.ClearableFileInput(attrs={'class':'form-control form-control-sm'}))
#     profile_notes = forms.CharField(label=_("Notes (Profile)"), widget=forms.Textarea(attrs={'rows':3, 'class':'form-control form-control-sm'}), required=False)

#     class Meta(UserCreationForm.Meta):
#         model = StaffUser
#         # UserCreationForm needs the USERNAME_FIELD ('email') and password fields.
#         # It will provide 'email', 'password21', 'password2'.
#         # We explicitly add 'first_name' and 'last_name' here if we want them handled by the form's core model
#         # processing, or we can handle them purely in the save() method from the form's declared fields.
#         # Listing them here makes them "model fields" for the UserCreationForm's Meta.
#         fields = ("email", "first_name", "last_name") # These are for the StaffUser model part

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         # Customize the 'email' field provided by UserCreationForm
#         if 'email' in self.fields:
#             self.fields['email'].widget.attrs.update({'class':'form-control form-control-sm', 'placeholder': '<EMAIL>'})
#             self.fields['email'].required = True # Ensure email is required
        
#         # You can add placeholders or further customize other fields here if needed
#         # For example, for password fields if you want to add placeholders:
#         # self.fields['password21'].widget.attrs.update({'class':'form-control form-control-sm', 'placeholder': 'Enter password'})
#         # self.fields['password22'].widget.attrs.update({'class':'form-control form-control-sm', 'placeholder': 'Confirm password'})


#     def clean_employee_id(self):
#         employee_id = self.cleaned_data.get('employee_id')
#         if employee_id:
#             # This check is tenant-aware because StaffUser.objects queries within the current tenant.
#             queryset = StaffUser.objects.filter(employee_id__iexact=employee_id)
#             if self.instance and self.instance.pk: # If form is bound to an existing instance (update)
#                 queryset = queryset.exclude(pk=self.instance.pk)
#             if queryset.exists():
#                 raise forms.ValidationError(_("This Employee ID is already in use by another staff member."))
#         return employee_id
    
#     # Optional: If you want to ensure first_name and last_name are provided,
#     # even though UserCreationForm might not enforce it by default if they are not in REQUIRED_FIELDS
#     # (but they are in your StaffUser.REQUIRED_FIELDS, so UserCreationForm should validate them)
#     # def clean(self):
#     #     cleaned_data = super().clean()
#     #     if not cleaned_data.get('first_name'):
#     #         self.add_error('first_name', _("This field is required."))
#     #     if not cleaned_data.get('last_name'):
#     #         self.add_error('last_name', _("This field is required."))
#     #     return cleaned_data

#     @transaction.atomic
#     def save(self, commit=True):
#         user = super().save(commit=False) # Creates StaffUser with email, hashed_password, first_name, last_name

#         # Set additional StaffUser fields (those not handled by UserCreationForm.Meta.fields)
#         # first_name and last_name are already set by super().save() if they were in Meta.fields
#         # If they were not in Meta.fields, you'd set them here:
#         # user.first_name = self.cleaned_data.get('first_name')
#         # user.last_name = self.cleaned_data.get('last_name')

#         user.employee_id = self.cleaned_data.get('employee_id')
#         user.designation = self.cleaned_data.get('designation')
#         user.department = self.cleaned_data.get('department')
#         user.date_hired = self.cleaned_data.get('date_hired')
#         user.phone_number = self.cleaned_data.get('phone_number_staff') # Ensure StaffUser model has 'phone_number'
        
#         user.is_staff = self.cleaned_data.get('is_staff_access', False) # For Django admin access
#         # UserCreationForm sets is_active=True by default.

#         if commit:
#             user.save() # Save the fully populated StaffUser

#             # EmployeeProfile fields
#             profile_data = {
#                 'middle_name': self.cleaned_data.get('profile_middle_name'),
#                 'gender': self.cleaned_data.get('profile_gender'),
#                 'date_of_birth': self.cleaned_data.get('profile_date_of_birth'),
#                 'marital_status': self.cleaned_data.get('profile_marital_status'),
#                 'phone_number_alternate': self.cleaned_data.get('profile_phone_number_alternate'),
#                 'address_line1': self.cleaned_data.get('profile_address_line1'),
#                 'address_line2': self.cleaned_data.get('profile_address_line2'),
#                 'city': self.cleaned_data.get('profile_city'),
#                 'state_province': self.cleaned_data.get('profile_state_province'),
#                 'postal_code': self.cleaned_data.get('profile_postal_code'),
#                 'country': self.cleaned_data.get('profile_country'),
#                 'employment_type': self.cleaned_data.get('profile_employment_type'),
#                 'notes': self.cleaned_data.get('profile_notes'),
#                 'photo': self.cleaned_data.get('profile_photo'), # Handles None if no photo uploaded
#             }
            
#             # Use update_or_create for EmployeeProfile
#             EmployeeProfile.objects.update_or_create(
#                 user=user,
#                 defaults=profile_data
#             )
#         return user

# # ========================================
# # FORM 2: Updating an EXISTING Staff Member (StaffUser + EmployeeProfile)
# # This form is used by views in apps.schools
# # ========================================

# class StaffAndProfileChangeForm(forms.ModelForm):
#     """
#     Form for editing an existing StaffUser and their linked EmployeeProfile.
#     Based on EmployeeProfile, with StaffUser fields added manually.
#     """
#     # --- StaffUser fields (Manually added to this form) ---
#     email = forms.EmailField(
#         disabled=True, required=False, label=_("Login Email (Cannot Change)"),
#         widget=forms.TextInput(attrs={'readonly':'readonly', 'class':'form-control-plaintext text-muted'}) # Added text-muted
#     )
#     first_name = forms.CharField(
#         label=_("First Name"), max_length=150, required=True,
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm'})
#     )
#     last_name = forms.CharField(
#         label=_("Last Name"), max_length=150, required=True,
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm'})
#     )
#     employee_id = forms.CharField(
#         label=_("Employee ID"), max_length=50, required=True,
#         help_text=_("School-specific unique employee ID."),
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm'})
#     )
#     designation = forms.CharField(
#         label=_("Designation/Job Title"), max_length=100, required=True,
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm'})
#     )
#     department = forms.CharField(
#         label=_("Department"), max_length=100, required=False,
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm'})
#     )
#     date_hired = forms.DateField(
#         label=_("Date Hired"),
#         widget=forms.DateInput(attrs={'type':'date', 'class':'form-control form-control-sm'}),
#         required=True
#     )
#     phone_number = forms.CharField( # Maps to StaffUser.phone_number
#         label=_("Staff Primary Phone"), max_length=30, required=False,
#         widget=forms.TextInput(attrs={'class':'form-control form-control-sm'})
#     )
#     is_active = forms.BooleanField( # Maps to StaffUser.is_active
#         label=_("Login Account Active"), required=False,
#         widget=forms.CheckboxInput(attrs={'class':'form-check-input'})
#     )
#     is_staff_flag = forms.BooleanField( # Maps to StaffUser.is_staff
#         label=_("Can Access Tenant Django Admin Site?"), required=False,
#         widget=forms.CheckboxInput(attrs={'class':'form-check-input'})
#     )

#     class Meta:
#         model = EmployeeProfile
#         fields = [
#             'middle_name', 'gender', 'date_of_birth', 'marital_status',
#             'phone_number_alternate', 'address_line1', 'address_line2', 'city',
#             'state_province', 'postal_code', 'country', 'employment_type',
#             'date_left', 'photo', 'notes',
#         ]
#         widgets = {
#             'middle_name': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
#             'gender': forms.Select(attrs={'class': 'form-select form-select-sm'}),
#             'date_of_birth': forms.DateInput(attrs={'type':'date', 'class':'form-control form-control-sm'}),
#             'marital_status': forms.Select(attrs={'class': 'form-select form-select-sm'}),
#             'phone_number_alternate': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
#             'address_line1': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
#             'address_line2': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
#             'city': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
#             'state_province': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
#             'postal_code': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
#             'country': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
#             'employment_type': forms.Select(attrs={'class': 'form-select form-select-sm'}),
#             'date_left': forms.DateInput(attrs={'type':'date', 'class':'form-control form-control-sm'}),
#             'photo': forms.ClearableFileInput(attrs={'class':'form-control form-control-sm'}),
#             'notes': forms.Textarea(attrs={'rows':3, 'class':'form-control form-control-sm'}),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         # self.instance is the EmployeeProfile instance
#         if self.instance and self.instance.pk: # Check if instance exists (it should for an update form)
#             user = self.instance.user # Get the related StaffUser
#             self.fields['email'].initial = user.email
#             self.fields['first_name'].initial = user.first_name
#             self.fields['last_name'].initial = user.last_name
#             self.fields['employee_id'].initial = user.employee_id
#             self.fields['designation'].initial = user.designation
#             self.fields['department'].initial = user.department
#             self.fields['date_hired'].initial = user.date_hired
#             self.fields['phone_number'].initial = user.phone_number # StaffUser.phone_number
#             self.fields['is_active'].initial = user.is_active       # StaffUser.is_active
#             self.fields['is_staff_flag'].initial = user.is_staff   # StaffUser.is_staff
#         # Set choices if they are dynamic or need to be set here, though usually ModelForm handles static choices
#         self.fields['gender'].choices = GENDER_CHOICES
#         self.fields['marital_status'].choices = MARITAL_STATUS_CHOICES
#         self.fields['employment_type'].choices = EMPLOYMENT_TYPE_CHOICES


#     def clean_employee_id(self):
#         employee_id = self.cleaned_data.get('employee_id')
#         # This check is tenant-aware as StaffUser.objects queries within the current tenant
#         if employee_id and self.instance and self.instance.user: # self.instance is EmployeeProfile
#             query = StaffUser.objects.exclude(pk=self.instance.user.pk)
#             if query.filter(employee_id__iexact=employee_id).exists():
#                 raise forms.ValidationError(_("This Employee ID is already in use by another staff member."))
#         # No need for the 'elif' part for a change form, as self.instance should always exist.
#         return employee_id

#     @transaction.atomic
#     def save(self, commit=True):
#         # First, get the EmployeeProfile instance from the ModelForm, but don't save yet.
#         # This instance will have updated fields from EmployeeProfile part of the form.
#         profile = super().save(commit=False)

#         # Get the related StaffUser from the profile instance
#         # It's crucial that self.instance (and thus profile) is correctly populated by the view
#         user = profile.user 

#         # Update StaffUser fields from the form's cleaned_data
#         user.first_name = self.cleaned_data['first_name']
#         user.last_name = self.cleaned_data['last_name']
#         user.employee_id = self.cleaned_data['employee_id']
#         user.designation = self.cleaned_data['designation']
#         user.department = self.cleaned_data.get('department') # Use .get() for optional fields
#         user.date_hired = self.cleaned_data['date_hired']
#         user.phone_number = self.cleaned_data.get('phone_number') # StaffUser.phone_number
        
#         # Boolean fields from checkboxes will be True/False in cleaned_data if required=False
#         user.is_active = self.cleaned_data.get('is_active', False) 
#         user.is_staff = self.cleaned_data.get('is_staff_flag', False)

#         if commit:
#             user.save()    # Save changes to StaffUser
#             profile.save() # Save changes to EmployeeProfile
#             # self.save_m2m() # Call if ModelForm had m2m fields, EmployeeProfile doesn't here
#         return profile # Return the EmployeeProfile instance

# ========================================
# FORM 3: LeaveType CRUD
# ========================================


# D:\school_fees_saas_v2\apps\hr\forms.py
from django import forms
from django.utils.translation import gettext_lazy as _
from .models import LeaveType

from django import forms
from .models import LeaveType

class LeaveTypeForm(forms.ModelForm):
    class Meta:
        model = LeaveType
        # --- THIS IS THE CORRECTED 'fields' LIST ---
        fields = [
            'name', 
            'description', 
            'is_paid', 
            'requires_approval', 
            'is_active',
            # Fields for non-accruing leave types
            'max_days_per_year_grant',
            # Fields for the new accrual system
            'accrual_frequency', 
            'accrual_rate',
            'max_accrual_balance',
            'prorate_accrual',
        ]
        # You can add widgets here to style the form if you like
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_paid': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'requires_approval': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            
            'max_days_per_year_grant': forms.NumberInput(attrs={'class': 'form-control'}),
            
            'accrual_frequency': forms.Select(attrs={'class': 'form-select'}),
            'accrual_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'max_accrual_balance': forms.NumberInput(attrs={'class': 'form-control'}),
            'prorate_accrual': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # You can add JavaScript-driven show/hide logic here later
        # For example, only show 'accrual_rate' if 'accrual_frequency' is 'MONTHLY'.
        # For now, all fields will be visible.



# ========================================
# FORM 4: LeaveRequest CRUD (for Staff submission)
# ========================================
from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal

from .models import LeaveType, LeaveRequest, LeaveBalance # Ensure these are correct imports
# from apps.users.models import EmployeeProfile # Or wherever your EmployeeProfile is defined and imported from

class StaffLeaveRequestForm(forms.ModelForm): # <--- THIS IS THE FORM IN QUESTION
    class Meta:
        model = LeaveRequest
        fields = [
            'leave_type', 
            'start_date', 
            'end_date', 
            'reason', 
            'half_day_start', 
            'half_day_end', 
            'attachment'  # <--- ENSURE 'attachment' IS IN THIS LIST
        ]
        widgets = {
            'leave_type': forms.Select(attrs={'class': 'form-select'}),
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 3, 'class': 'form-control', 'placeholder': 'Reason for leave...'}),
            'half_day_start': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'half_day_end': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'attachment': forms.ClearableFileInput(attrs={'class': 'form-control'}), # <--- ENSURE WIDGET FOR attachment IS HERE
        }
        labels = { 
            'half_day_start': _("Request first half of start day as leave"),
            'half_day_end': _("Request last half of end day as leave"),
            'attachment': _("Attach Supporting Document"), # Optional: custom label
        }
        help_texts = {
            'attachment': _("Optional. Max file size: 5MB. Allowed types: PDF, JPG, PNG."), # Optional: custom help text
        }


    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None) # Pop user passed from view's get_form_kwargs
        self.employee = kwargs.pop('employee', None) # Pop employee passed from view's get_form_kwargs
        super().__init__(*args, **kwargs)
        self.fields['leave_type'].queryset = LeaveType.objects.all().order_by('name') # Or filter active

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")
        leave_type = cleaned_data.get("leave_type")
        half_day_start = cleaned_data.get("half_day_start")
        half_day_end = cleaned_data.get("half_day_end")

        if not self.user or not hasattr(self.user, 'hr_profile'):
            # This should ideally be caught in the view before form instantiation
            raise ValidationError("Cannot process request: Employee profile not found.")

        employee_profile = self.user.hr_profile

        if start_date and end_date:
            if end_date < start_date:
                self.add_error('end_date', _("End date cannot be before start date."))
            else:
                # Calculate number of days (using a placeholder model instance for calculation)
                temp_request = LeaveRequest(
                    start_date=start_date, end_date=end_date,
                    half_day_start=half_day_start, half_day_end=half_day_end
                )
                num_days_requested = temp_request.calculate_number_of_days()
                cleaned_data['number_of_days'] = num_days_requested # Store for use if needed

                if num_days_requested <= 0:
                    # Allow 0 if it's a single day marked as both half_day_start and half_day_end (effectively full day off but 0 working days consumed)
                    # Or if it's a single half day (0.5 day).
                    # This logic depends on how calculate_number_of_days handles it.
                    # If calculate_number_of_days can return 0.5, then checking for <= 0 is fine.
                    # Let's assume calculate_number_of_days always returns >= 0.
                    if num_days_requested == 0 and not (half_day_start and half_day_end and start_date == end_date):
                        self.add_error(None, _("Calculated leave duration is zero. Check dates and half-day selections."))


                # --- Leave Balance Check ---
                if leave_type and num_days_requested > 0:
                    try:
                        # Get balance for the specific leave type and employee
                        # Add academic_year or period_info filtering if balances are yearly/periodic
                        balance = LeaveBalance.objects.get(
                            employee=employee_profile,
                            leave_type=leave_type
                            # year_or_period_info=... # Add if your balances are yearly
                        )
                        if balance.days_remaining < num_days_requested:
                            self.add_error(None, # Non-field error
                                f"Insufficient '{leave_type.name}' balance. "
                                f"Requested: {num_days_requested} days, Available: {balance.days_remaining} days."
                            )
                    except LeaveBalance.DoesNotExist:
                        # If no balance record, assume 0 balance unless leave type allows negative
                        # or is a type that doesn't need pre-accrual (e.g., Unpaid Leave)
                        if leave_type.max_annual_days > 0 or leave_type.is_paid : # Only check balance for types that typically have one
                            self.add_error('leave_type',
                                f"No leave balance record found for '{leave_type.name}'. "
                                f"Please contact HR to set up your entitlement for this leave type."
                            )
                # --- End Leave Balance Check ---

                # TODO: Check for overlapping approved/pending leave requests
                # overlapping_requests = LeaveRequest.objects.filter(...)
                # if overlapping_requests.exists():
                #    self.add_error(None, "You have an overlapping leave request for this period.")

        return cleaned_data


# ========================================
# FORM 5: AdminLeaveRequestUpdateForm (for Admin/Manager approval)
# ========================================
class AdminLeaveRequestUpdateForm(forms.ModelForm):
    class Meta:
        model = LeaveRequest
        fields = ['status', 'admin_notes'] # Only fields admin can change
        widgets = {
            'status': forms.Select(attrs={'class': 'form-select'}),
            'admin_notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }
        labels = {
            'admin_notes': _("Notes (Visible to Staff)"),
        }

# ========================================
# FORM 6: LeaveBalanceForm (Optional - for manual admin adjustments)
# ========================================
class LeaveBalanceForm(forms.ModelForm):
    class Meta:
        model = LeaveBalance
        # Fields admin might adjust. Employee & LeaveType usually set by context or read-only.
        fields = ['employee', 'leave_type', 'days_accrued', 'days_taken', 'year_or_period_info']
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-select', 'disabled': 'disabled'}), # Usually read-only here
            'leave_type': forms.Select(attrs={'class': 'form-select', 'disabled': 'disabled'}), # Usually read-only here
            'days_accrued': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5'}),
            'days_taken': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5'}),
            'year_or_period_info': forms.TextInput(attrs={'class':'form-control', 'placeholder': 'e.g., Accrual for 2024'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If instance is provided, make employee and leave_type read-only
        if self.instance and self.instance.pk:
            self.fields['employee'].disabled = True
            self.fields['leave_type'].disabled = True
        else: # For creation, admin would select these
            # This form is more suited for UPDATE. For CREATE, might need to pass choices.
            # from apps.schools.models import StaffUser
            # self.fields['employee'].queryset = EmployeeProfile.objects.select_related('user').order_by('user__last_name')
            # self.fields['leave_type'].queryset = LeaveType.objects.all().order_by('name')
            pass

# D:\school_fees_saas_v2\apps\hr\forms.py

from django import forms
from .models import (
    SalaryComponent, 
    TaxBracket, 
    SalaryGrade, 
    # SalaryGradeComponent, # This is the model for the formset
    # StaffSalary
)

# --- SETUP FORMS ---

class SalaryComponentForm(forms.ModelForm):
    class Meta:
        model = SalaryComponent
        # This list now correctly uses the actual field names from the model
        fields = ['name', 'component_type', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'component_type': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }
        
        
class TaxBracketForm(forms.ModelForm):
    class Meta:
        model = TaxBracket
        fields = ['name', 'from_amount', 'to_amount', 'rate_percent', 'deduction_amount', 'is_active']
        # Add widgets for styling
        # ...

# --- NEW FORMS FOR SALARY GRADE STRUCTURE ---

# class SalaryGradeForm(forms.ModelForm):
#     """Form for creating or editing a SalaryGrade (the template)."""
#     class Meta:
#         model = SalaryGrade
#         fields = ['name', 'description', 'is_active']
#         widgets = {
#             'name': forms.TextInput(attrs={'class': 'form-control'}),
#             'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
#             'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input', 'role': 'switch'}),
#         }

# SalaryGradeComponentFormSet = forms.inlineformset_factory(
#     parent_model=SalaryGrade,         # The parent is now SalaryGrade
#     model=SalaryGradeComponent,       # The child is SalaryGradeComponent
#     fields=('component', 'amount'),
#     extra=1,
#     can_delete=True,
#     widgets={
#         'component': forms.Select(attrs={'class': 'form-select'}),
#         'amount': forms.NumberInput(attrs={'class': 'form-control'}),
#     }
# )

# --- FORM FOR ASSIGNING A GRADE TO A STAFF MEMBER ---

# class StaffSalaryForm(forms.ModelForm):
#     """
#     Form for editing a staff member's salary details.
#     This form assigns a grade and sets the basic salary.
#     """
#     class Meta:
#         model = StaffSalary
#         fields = ['grade', 'basic_salary', 'effective_date']
#         widgets = {
#             'grade': forms.Select(attrs={'class': 'form-select'}),
#             'basic_salary': forms.NumberInput(attrs={'class': 'form-control'}),
#             'effective_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         # Filter the grade dropdown to only show active grades
#         self.fields['grade'].queryset = SalaryGrade.objects.filter(is_active=True)


# --- PAYROLL RUN FORM ---
class PayrollPeriodForm(forms.Form):
    pay_period = forms.DateField(
        label="Select Month for Payroll Run",
        widget=forms.DateInput(attrs={'type': 'month', 'class': 'form-control'})
    )
    



# D:\school_fees_saas_v2\apps\hr\forms.py

from django import forms
from django.utils import timezone
from .models import (
    SalaryComponent, 
    TaxBracket, 
    SalaryGrade, 
    # SalaryGradeComponent,
    # StaffSalary
)

# ==============================================================================
# 1. FORM FOR SALARY COMPONENTS
# ==============================================================================


# ==============================================================================
# 2. FORM FOR TAX BRACKETS
# ==============================================================================
class TaxBracketForm(forms.ModelForm):
    class Meta:
        model = TaxBracket
        fields = ['name', 'from_amount', 'to_amount', 'rate_percent', 'deduction_amount', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'from_amount': forms.NumberInput(attrs={'class': 'form-control'}),
            'to_amount': forms.NumberInput(attrs={'class': 'form-control'}),
            'rate_percent': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'deduction_amount': forms.NumberInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input', 'role': 'switch'}),
        }


# ==============================================================================
# 3. FORMS FOR SALARY GRADES AND THEIR COMPONENTS
# # ==============================================================================


# ==============================================================================
# 4. FORM FOR ASSIGNING SALARY DETAILS TO A STAFF MEMBER
# # ==============================================================================
# class StaffSalaryForm(forms.ModelForm):
#     """Form for editing a staff member's specific salary details."""
#     class Meta:
#         model = StaffSalary
#         fields = ['grade', 'basic_salary', 'effective_date']
#         widgets = {
#             'grade': forms.Select(attrs={'class': 'form-select'}),
#             'basic_salary': forms.NumberInput(attrs={'class': 'form-control'}),
#             'effective_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
#         }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)
#         # Ensure the dropdown for 'grade' only shows active Salary Grades
#         self.fields['grade'].queryset = SalaryGrade.objects.filter(is_active=True)
#         self.fields['grade'].empty_label = "--- Select a Salary Grade ---"


# ==============================================================================
# 5. FORM FOR INITIATING A PAYROLL RUN
# ==============================================================================
class PayrollPeriodForm(forms.Form):
    pay_period = forms.DateField(
        label="Select Month for Payroll Run",
        widget=forms.DateInput(attrs={'type': 'month', 'class': 'form-control'})
    )
    
    

# D:\school_fees_saas_v2\apps\hr\forms.py

from django import forms
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import date

class ProcessPayrollForm(forms.Form):
    """
    Form to select the month and year for which to process payroll.
    """
    # Create choices for months and years dynamically
    MONTH_CHOICES = [(i, date(2000, i, 1).strftime('%B')) for i in range(1, 13)]
    YEAR_CHOICES = [(i, i) for i in range(timezone.now().year - 2, timezone.now().year + 2)]

    month = forms.ChoiceField(
        choices=MONTH_CHOICES, 
        initial=timezone.now().month,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    year = forms.ChoiceField(
        choices=YEAR_CHOICES, 
        initial=timezone.now().year,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    payment_date = forms.DateField(
        label="Payment Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        help_text="The date salaries will be paid."
    )

    def clean(self):
        cleaned_data = super().clean()
        # You can add validation here, e.g., to prevent running payroll for the same
        # month/year twice.
        # from .models import PayrollRun
        # month = int(cleaned_data.get('month'))
        # year = int(cleaned_data.get('year'))
        # if PayrollRun.objects.filter(pay_period_start__month=month, pay_period_start__year=year).exists():
        #     raise forms.ValidationError("Payroll has already been processed for this month and year.")
        return cleaned_data
    



# D:\school_fees_saas_v2\apps\hr\forms.py

from django import forms
from django.forms import inlineformset_factory
from .models import StaffSalaryStructure, SalaryStructureComponent, SalaryComponent

class StaffSalaryStructureForm(forms.ModelForm):
    """
    Form for the main salary structure, linking it to a staff member.
    """
    class Meta:
        model = StaffSalaryStructure
        fields = ['staff_user', 'effective_date']
        # We will make the staff_user field read-only in the template,
        # as it will be set by the view.
        widgets = {
            'effective_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'staff_user': forms.Select(attrs={'class': 'form-control', 'disabled': 'disabled'}),
        }

class SalaryStructureComponentForm(forms.ModelForm):
    """
    Form for a single line item in the salary structure.
    """
    class Meta:
        model = SalaryStructureComponent
        fields = ['component', 'amount']
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Style the fields for Bootstrap
        self.fields['component'].widget.attrs.update({'class': 'form-select form-select-sm'})
        self.fields['amount'].widget.attrs.update({'class': 'form-control form-control-sm', 'placeholder': 'Amount'})
        # Make the dropdown more user-friendly
        self.fields['component'].empty_label = "Select Component..."


# Create the formset factory
# This links the parent (StaffSalaryStructure) to the child (SalaryStructureComponent)
SalaryStructureComponentFormSet = inlineformset_factory(
    StaffSalaryStructure,          # Parent model
    SalaryStructureComponent,      # Child model
    form=SalaryStructureComponentForm, # The form to use for each line item
    extra=1,                       # Start with one extra blank form
    can_delete=True,               # Allow users to delete line items
    can_delete_extra=True          # Allow deleting even the extra blank forms
)



# apps/hr/forms.py
from django import forms
from django.forms import inlineformset_factory
from .models import SalaryGrade, SalaryGradeComponent

class SalaryGradeForm(forms.ModelForm):
    class Meta:
        model = SalaryGrade
        fields = ['name', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


SalaryGradeComponentFormSet = inlineformset_factory(
    SalaryGrade,                 # Parent model
    SalaryGradeComponent,        # Child model
    fields=('component', 'amount'), # Fields to edit in the formset
    extra=1,                     # Start with one extra empty form
    can_delete=True,             # Allow deletion of existing components
    widgets={
        'component': forms.Select(attrs={'class': 'form-select form-select-sm'}),
        'amount': forms.NumberInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'Amount'}),
    }
)


