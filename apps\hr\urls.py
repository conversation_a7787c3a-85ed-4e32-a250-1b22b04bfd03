# D:\school_fees_saas_v2\apps\hr\urls.py
from django.urls import path
from . import views # Import views from the current hr app

app_name = 'hr' # Namespace for this app's URLs

urlpatterns = [
    # --- Leave Type URLs ---
    path('leave-types/', views.LeaveTypeListView.as_view(), name='leavetype_list'),
    path('leave-types/new/', views.LeaveTypeCreateView.as_view(), name='leavetype_create'),
    path('leave-types/<int:pk>/edit/', views.LeaveTypeUpdateView.as_view(), name='leavetype_update'),
    path('leave-types/<int:pk>/delete/', views.LeaveTypeDeleteView.as_view(), name='leavetype_delete'),

    path('my-dashboard/', views.StaffDashboardView.as_view(), name='staff_dashboard'),

    # Staff Leave Request URLs
    path('my-leave/', views.StaffLeaveRequestListView.as_view(), name='staff_leaverequest_list'),
    path('my-leave/apply/', views.StaffLeaveRequestCreateView.as_view(), name='staff_leaverequest_apply'),
    path('my-leave/<int:pk>/', views.StaffLeaveRequestDetailView.as_view(), name='staff_leaverequest_detail'),
    path('my-leave/<int:pk>/cancel/', views.StaffLeaveRequestCancelView.as_view(), name='staff_leaverequest_cancel'),
    
    
    # --- ADMIN/MANAGER LEAVE REQUESTS ---
    # Add this line for the list view:
    path('leave-requests/', views.AdminLeaveRequestListView.as_view(), name='admin_leaverequest_list'),

    # Your existing update status URL:
    path('leave-requests/<int:pk>/update-status/', views.LeaveRequestAdminUpdateView.as_view(), name='leave_request_admin_update'), # Assuming AdminLeaveRequestUpdateView exists

    # Leave Balances
    path('leave-balances/', views.LeaveBalanceListView.as_view(), name='leave_balance_list'), # Assuming LeaveBalanceListView exists
    
    # --- NEW URL for My Leave Summary (Staff facing) ---
    path('my-leave-summary/', views.my_leave_summary_view, name='my_leave_summary'),
    
    # --- SALARY GRADE URLS ---
    path('payroll/grades/', views.SalaryGradeListView.as_view(), name='salarygrade_list'),
    # path('payroll/grades/new/', views.SalaryGradeCreateView.as_view(), name='salarygrade_create'),
    # path('payroll/grades/<int:pk>/edit/', views.SalaryGradeUpdateView.as_view(), name='salarygrade_update'),
    path('payroll/grades/<int:pk>/delete/', views.SalaryGradeDeleteView.as_view(), name='salarygrade_delete'),
    
    
    # --- SALARY COMPONENT URLS ---
    path('payroll/components/', views.SalaryComponentListView.as_view(), name='salarycomponent_list'),
    path('payroll/components/new/', views.SalaryComponentCreateView.as_view(), name='salarycomponent_create'),
    path('payroll/components/<int:pk>/edit/', views.SalaryComponentUpdateView.as_view(), name='salarycomponent_update'),
    # Add a DeleteView/URL if needed

    # --- TAX BRACKET URLS ---
    path('payroll/tax-brackets/', views.TaxBracketListView.as_view(), name='taxbracket_list'),
    path('payroll/tax-brackets/new/', views.TaxBracketCreateView.as_view(), name='taxbracket_create'),
    path('payroll/tax-brackets/<int:pk>/edit/', views.TaxBracketUpdateView.as_view(), name='taxbracket_update'),
    # Add a DeleteView/URL if needed
    
    # --- SALARY GRADE URLS ---
    path('payroll/grades/', views.SalaryGradeListView.as_view(), name='salarygrade_list'),
    # path('payroll/grades/new/', views.SalaryGradeCreateView.as_view(), name='salarygrade_create'),
    # path('payroll/grades/<int:pk>/edit/', views.SalaryGradeUpdateView.as_view(), name='salarygrade_update'),
    path('payroll/grades/<int:pk>/delete/', views.SalaryGradeDeleteView.as_view(), name='salarygrade_delete'),

    # --- STAFF SALARY ASSIGNMENT URL ---
    # This URL takes the staff member's PK to know whose salary to edit
    # path('staff/<int:staff_pk>/salary/', views.StaffSalaryUpdateView.as_view(), name='staffsalary_update'),
    
    
    path('payslips/<int:pk>/pdf/', views.PayslipPDFView.as_view(), name='payslip_pdf'),
    
    
    # --- NEW PAYROLL URLS ---
    # List of all payroll runs
    path('payroll/', views.PayrollRunListView.as_view(), name='payroll_run_list'),
    
    # Detail view for a single payroll run, showing all its payslips
    path('payroll/<int:pk>/', views.PayrollRunDetailView.as_view(), name='payroll_run_detail'),
    
    
    # --- THIS IS THE MISSING URL PATTERN ---
    path('payroll/<int:pk>/mark-paid/', views.MarkPayrollPaidView.as_view(), name='payroll_run_mark_paid'),

    # The existing URL for viewing a single payslip PDF
    path('payslips/<int:pk>/pdf/', views.PayslipPDFView.as_view(), name='payslip_pdf'),

    # (You'll add URLs for processing payroll later)
    path('payroll/process/', views.ProcessPayrollView.as_view(), name='payroll_process'),
    
    
    path('staff/<int:staff_pk>/salary/', views.StaffSalaryStructureUpdateView.as_view(), name='staff_salary_update'),
    
    # --- NEW PAYROLL PERIOD URLS ---
    # path('payroll-periods/', views.PayrollPeriodListView.as_view(), name='payroll_period_list'),
    # path('payroll-periods/new/', views.PayrollPeriodCreateView.as_view(), name='payroll_period_create'),
    # path('payroll-periods/<int:pk>/edit/', views.PayrollPeriodUpdateView.as_view(), name='payroll_period_update'),

    # Salary Grade URLs
    path('payroll/grades/', views.SalaryGradeListView.as_view(), name='salarygrade_list'),
    path('payroll/grade/create/', views.SalaryGradeCreateView.as_view(), name='salarygrade_create'),
    path('payroll/grade/<int:pk>/update/', views.SalaryGradeUpdateView.as_view(), name='salarygrade_update'),
    
    # NEW URL FOR STAFF TO VIEW THEIR OWN PAYSLIPS
    path('my-payslips/', views.StaffPayslipListView.as_view(), name='my_payslip_list'),
    
    # NEW URL FOR THE SUMMARY REPORT
    path('reports/payroll-summary/', views.PayrollSummaryReportView.as_view(), name='payroll_summary_report'),
    
    # --- Statutory Deduction URLs ---
    path('payroll/deductions/', views.StatutoryDeductionListView.as_view(), name='deduction_list'),
    path('payroll/deduction/create/', views.StatutoryDeductionCreateView.as_view(), name='deduction_create'),
    path('payroll/deduction/<int:pk>/update/', views.StatutoryDeductionUpdateView.as_view(), name='deduction_update'),
    path('payroll/deduction/<int:pk>/delete/', views.StatutoryDeductionDeleteView.as_view(), name='deduction_delete'),
    
    
    # This URL is for admins/managers to view and action a specific leave request.
    path('leave-requests/<int:pk>/review/', views.AdminLeaveRequestUpdateView.as_view(), name='admin_leaverequest_update'),


]
