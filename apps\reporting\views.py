# D:\school_fees_saas_v2\apps\reporting\views.py

from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import ListView, TemplateView
from django.db.models import (
    Sum, F, Q, ExpressionWrapper, DecimalField, Case, When, Value, Count, OuterRef, Subquery
)

from django.db.models import Sum, Q, Case, When, Value, DecimalField, CharField
from django.http import HttpResponse

from django.db.models.functions import Coalesce
from django.utils import timezone
from django.http import HttpResponse, Http404
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from decimal import Decimal, InvalidOperation
import csv
import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from io import BytesIO
import logging
from django.conf import settings # For accounting codes

# --- Authentication & Permissions ---
from django.views.generic.edit import FormMixin
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin

# --- Model Imports ---
from apps.students.models import Student
from apps.fees.models import Invoice, AcademicYear, Term, StudentConcession
from apps.payments.models import Payment, PaymentMethod
from apps.finance.models import Expense, BudgetAmount, BudgetItem, ExpenseCategory, Vendor
from apps.accounting.models import Account as ChartOfAccount, Account, JournalLine, AccountType
from apps.schools.models import SchoolProfile, SchoolClass

# --- Forms (from this reporting app) ---
from .forms import (
    DateRangeForm,
    DateRangeClassTermForm,
    DateRangeAccountForm,
    IncomeExpenseReportForm,
    BudgetVarianceReportForm,
    ReportPeriodForm,
    BudgetReportFilterForm
    
)

from apps.fees.models import Invoice
from apps.finance.models import Expense, Budget
from apps.payments.models import Payment
from apps.common.utils import render_to_pdf, PDF_AVAILABLE # Ensure PDF_AVAILABLE is handled
from .forms import (
    BalanceSheetFilterForm,
    IncomeStatementFilterForm,
    TrialBalanceFilterForm,
    AgedReceivablesFilterForm,
    AgedPayablesFilterForm,
    CashFlowFilterForm, # Assuming you have this
    # ... any other report filter forms
)

# from django.views.generic import ListView # Assuming it's a ListView
# from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin # Your mixins
# from apps.payments.models import Payment # Example model for collection report
# from apps.fees.models import Invoice # Example
# from .forms import CollectionReportFilter 

from apps.fees.models import Invoice, ConcessionType as Concession

from apps.accounting.models import AccountType, Account, GeneralLedger

from django.db.models.query import QuerySet

# CHANGE THIS IMPORT
from .forms import StudentLedgerFilterForm

logger = logging.getLogger(__name__)

# ==============================================================================
# Base Report View Mixin -----MAIN ONE
# ==============================================================================

# D:\school_fees_saas_v2\apps\common\mixins.py (or wherever BaseReportViewMixin is)
from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
# ... other necessary imports ...
import logging
logger = logging.getLogger(__name__)

    
# ==============================================================================
# Outstanding Fees Report
# ==============================================================================

# apps/reporting/views.py
from django.shortcuts import render # Example existing import
from django.views.generic import ListView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.db.models import Sum, F, Case, When, Value, DecimalField, OuterRef, Subquery, ExpressionWrapper # Keep these
from django.db.models.functions import Coalesce # Keep this
from decimal import Decimal # Keep this

from apps.students.models import Student # You already have this for self.model
from apps.fees.models import Invoice, InvoiceDetail  # <<<< ADD THIS LINE
# from ..fees.models import Invoice, InvoiceDetail # Alternative relative import if preferred

from apps.common.mixins import BaseReportViewMixin # Your mixin
from .forms import DateRangeClassTermForm # Your form


# apps/reporting/views.py
import logging
from decimal import Decimal
from django.views.generic import ListView # ListView is appropriate here
from django.db.models import Sum, F, Q, ExpressionWrapper, DecimalField, Value, OuterRef, Subquery
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _

# Assuming BaseReportViewMixin is defined (it provides the main get_context_data structure)
# from .base_views import BaseReportViewMixin # Or from where it's defined
from apps.students.models import Student
from apps.fees.models import Invoice, InvoiceDetail 
# from apps.schools.models import SchoolClass # Needed if filter form or display needs it

# Assuming DateRangeClassTermForm is a django_filters.FilterSet
from .filters import DateRangeClassTermForm # Or from wherever filters are defined

logger = logging.getLogger(__name__)



# apps/reporting/views.py
import logging
from decimal import Decimal
from django.views.generic import ListView
from django.urls import reverse_lazy
from django.db.models import (
    Sum, F, Q, ExpressionWrapper, DecimalField, Value, OuterRef, Subquery, Model
) # Added Model for isinstance check in get_queryset
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _

# Assuming BaseReportViewMixin is defined (it provides the main get_context_data structure)
# from .base_views import BaseReportViewMixin 
from apps.students.models import Student
from apps.fees.models import Invoice, InvoiceDetail # CRITICAL: Ensure InvoiceStatus is defined in Invoice
# from apps.schools.models import SchoolClass # Needed if filter form or display needs it

# Assuming DateRangeClassTermForm is a django_filters.FilterSet
from .filters import DateRangeClassTermForm # Or from wherever filters are defined

logger = logging.getLogger(__name__)


# D:\school_fees_saas_v2\apps\reporting\views.py
from django.http import HttpResponse
from django.views.generic import ListView
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models import Sum, F, Q, ExpressionWrapper, DecimalField, Value, OuterRef, Subquery, Case, When, CharField
from django.db.models.functions import Coalesce
from decimal import Decimal
from django.shortcuts import redirect
from django.contrib import messages
import csv
from io import BytesIO
import openpyxl
from openpyxl.styles import Font
from openpyxl.utils import get_column_letter

from apps.common.mixins import TenantLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin
from apps.students.models import Student, SchoolClass, Section # Assuming SchoolClass, Section are in students or schools
from apps.fees.models import Invoice, InvoiceDetail # Import Invoice, InvoiceDetail
from apps.payments.models import PaymentAllocation # For more accurate paid amounts if needed
# from .forms import DateRangeClassTermForm # Assuming your filter form is defined
# from .filters import StudentOutstandingFeesFilterSet # Assuming your FilterSet is defined

import logging
logger = logging.getLogger(__name__)


class OutstandingFeesReportView(TenantLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin, ListView):
    model = Student # The primary listing is of students
    template_name = 'reporting/outstanding_fees_report.html' # Ensure this template exists
    context_object_name = 'students_report'
    paginate_by = 25

    permission_required = 'reporting.view_outstanding_fees_report'
    report_title = _("Outstanding Fees Report") # Can be set as class attribute
    report_code = 'RPT_OUTSTANDING_FEES'
    filter_form_class = None  # We'll use manual filtering for now

    def get_queryset(self):
        logger.debug(f"[{self.__class__.__name__}] get_queryset started. Request GET: {self.request.GET}")
        
        # Base queryset of active students
        base_queryset = Student.objects.filter(is_active=True).select_related(
            'current_class', 'current_section'
        ).prefetch_related(
            'parents', 'invoices', 'invoices__details' # Prefetch invoices and their details
        )

        # --- Apply Manual Filters from GET parameters ---
        queryset_after_form_filters = base_queryset

        # Apply class filter
        class_filter = self.request.GET.get('class', '')
        if class_filter:
            try:
                from apps.schools.models import SchoolClass
                class_obj = SchoolClass.objects.get(pk=class_filter)
                queryset_after_form_filters = queryset_after_form_filters.filter(current_class=class_obj)
            except (SchoolClass.DoesNotExist, ValueError):
                pass

        # Apply minimum due amount filter (will be applied after annotation)
        
        # --- Define outstanding invoice statuses ---
        try:
            from apps.fees.models import InvoiceStatus
            outstanding_statuses = [
                InvoiceStatus.SENT,
                InvoiceStatus.PARTIALLY_PAID,
                InvoiceStatus.OVERDUE
            ]
        except (AttributeError, ImportError):
            logger.error(f"CRITICAL: {self.__class__.__name__} - InvoiceStatus enum not defined correctly!")
            messages.error(self.request, _("System configuration error: Invoice statuses are not set up."))
            return Student.objects.none()

        # --- Annotate students with their outstanding balance ---
        # This approach calculates balance per student by summing balances of their outstanding invoices.
        # It calculates total from `subtotal_amount` - `total_concession_amount` and uses `amount_paid` field.
        
        # Subquery to calculate balance for each invoice
        invoice_balance_subquery = Invoice.objects.filter(
            pk=OuterRef('pk'), # Relates to the invoice being annotated
            status__in=outstanding_statuses
        ).annotate(
            calculated_balance=ExpressionWrapper(
                (Coalesce(F('subtotal_amount'), Decimal('0.00')) - Coalesce(F('total_concession_amount'), Decimal('0.00'))) - Coalesce(F('amount_paid'), Decimal('0.00')),
                output_field=DecimalField()
            )
        ).values('calculated_balance')

        # Subquery to sum balances of all outstanding invoices for a student
        student_outstanding_balance_subquery = Invoice.objects.filter(
            student_id=OuterRef('pk'), # OuterRef to Student's pk
            status__in=outstanding_statuses
        ).annotate(
            # Calculate balance for each invoice within this subquery scope
            balance_per_invoice=ExpressionWrapper(
                (Coalesce(F('subtotal_amount'), Decimal('0.00')) - Coalesce(F('total_concession_amount'), Decimal('0.00'))) - Coalesce(F('amount_paid'), Decimal('0.00')),
                output_field=DecimalField()
            )
        ).filter(balance_per_invoice__gt=Decimal('0.00')).values('student_id').annotate(
            total_student_outstanding=Sum('balance_per_invoice')
        ).values('total_student_outstanding')

        # Enhanced annotations to provide detailed financial breakdown
        # Let's try a simpler approach first to debug

        annotated_queryset = queryset_after_form_filters.annotate(
            # Outstanding balance (existing)
            outstanding_balance=Coalesce(
                Subquery(student_outstanding_balance_subquery[:1]),
                Decimal('0.00'),
                output_field=DecimalField()
            )
        )

        # Add financial annotations using direct aggregation on the reverse relationship
        annotated_queryset = annotated_queryset.annotate(
            # Use direct aggregation on the reverse relationship
            total_billed=Coalesce(
                Sum('invoices__subtotal_amount'),
                Decimal('0.00'),
                output_field=DecimalField()
            ),
            total_discount=Coalesce(
                Sum('invoices__total_concession_amount'),
                Decimal('0.00'),
                output_field=DecimalField()
            ),
            total_paid=Coalesce(
                Sum('invoices__amount_paid'),
                Decimal('0.00'),
                output_field=DecimalField()
            )
        )

        # Filter out students with no outstanding balance
        final_queryset = annotated_queryset.filter(outstanding_balance__gt=Decimal('0.005')).distinct() # Use small threshold for floating point

        # Apply minimum due amount filter after annotation
        min_due_filter = self.request.GET.get('min_due', '')
        if min_due_filter:
            try:
                min_due_amount = Decimal(min_due_filter)
                final_queryset = final_queryset.filter(outstanding_balance__gte=min_due_amount)
            except (ValueError, InvalidOperation):
                pass
        
        # Order the results
        final_queryset = final_queryset.order_by('current_class__name', 'current_section__name', 'last_name', 'first_name')
        
        logger.debug(f"[{self.__class__.__name__}] get_queryset final count: {final_queryset.count()}")
        
        # Store the full, unfiltered (by pagination) queryset for aggregation in get_report_data
        self.full_report_queryset = final_queryset



        return final_queryset # This will be paginated by ListView

    def get_report_data(self, filter_form=None):
        """
        Calculates summary data based on the self.full_report_queryset.
        """
        logger.debug(f"[{self.__class__.__name__}] get_report_data called.")
        report_specific_data = {}

        if hasattr(self, 'full_report_queryset') and self.full_report_queryset is not None:
            total_outstanding_all_students = self.full_report_queryset.aggregate(
                total_sum=Sum('outstanding_balance') # Sum the 'outstanding_balance' annotation
            )['total_sum'] or Decimal('0.00')
            
            report_specific_data['total_outstanding_all_students'] = total_outstanding_all_students
            logger.debug(f"[{self.__class__.__name__}] Total outstanding from 'get_report_data': {total_outstanding_all_students}")
        else:
            logger.warning(f"[{self.__class__.__name__}] full_report_queryset not available for aggregation in get_report_data.")
            report_specific_data['total_outstanding_all_students'] = Decimal('0.00')
        
        return report_specific_data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add manual filter handling since we're not using a form class
        current_class_filter = self.request.GET.get('class', '')
        current_min_due_filter = self.request.GET.get('min_due', '')

        # Get available classes for the filter dropdown
        from apps.schools.models import SchoolClass
        available_classes = SchoolClass.objects.filter(is_active=True).order_by('name')

        # Add filter context
        context.update({
            'current_class_filter': current_class_filter,
            'current_min_due_filter': current_min_due_filter,
            'available_classes': available_classes,
            'overall_total_due': self.get_report_data().get('total_outstanding_all_students', Decimal('0.00')),
        })

        # Add school profile for currency symbol
        try:
            from apps.schools.models import SchoolProfile
            context['school_profile'] = SchoolProfile.objects.first()
        except:
            context['school_profile'] = None

        return context

    # --- Export Methods ---
    # These methods use self.full_report_queryset (all filtered results, not just current page)

    def get_export_queryset(self):
        """Returns the full, filtered queryset for export."""
        if not hasattr(self, 'full_report_queryset') or self.full_report_queryset is None:
            # If exports are called directly without ListView's get path (e.g. via a separate button POST)
            # we need to re-run get_queryset to establish self.full_report_queryset.
            # ListView's get_queryset populates self.object_list which then gets assigned to full_report_queryset.
            # For a direct export call, we need to ensure this setup.
            # A simple way is to call get_queryset if it's not already set by a prior GET request processing.
            # However, BaseReportViewMixin should ideally handle making the full_report_queryset available
            # even if it's an export request.
            # For now, let's assume it's available. If not, this is a point of refinement in BaseReportViewMixin.
            logger.warning(f"[{self.__class__.__name__}] get_export_queryset: full_report_queryset not set. Attempting to generate.")
            # This might re-run filters if not careful.
            # A better pattern in BaseReportViewMixin might be to always build the full_report_queryset
            # in get_context_data or a similar shared method, and ListView uses that.
            # For now, we'll just use what get_queryset sets.
            self.object_list = self.get_queryset() # This sets self.full_report_queryset

        return self.full_report_queryset


    def export_to_csv(self, queryset_for_export, request):
        queryset = queryset_for_export or self.get_export_queryset()
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.csv"'
        writer = csv.writer(response)
        
        headers = [
            _('Admission No'), _('Full Name'), _('Class'), _('Section'), 
            _('Primary Parent'), _('Parent Phone'), _('Outstanding Balance')
        ]
        writer.writerow(headers)

        for student in queryset:
            primary_parent = student.parents.filter(is_primary_guardian=True).first() # Assuming is_primary_guardian field
            if not primary_parent and student.parents.exists():
                primary_parent = student.parents.first()

            writer.writerow([
                student.admission_number, 
                student.full_name,
                student.current_class.name if student.current_class else 'N/A',
                student.current_section.name if student.current_section else 'N/A',
                primary_parent.get_full_name() if primary_parent else 'N/A',
                primary_parent.phone_number if primary_parent and primary_parent.phone_number else 'N/A',
                f"{student.outstanding_balance:.2f}" # Ensure outstanding_balance is available
            ])
        return response

    def export_to_excel(self, queryset_for_export, request):
        queryset = queryset_for_export or self.get_export_queryset()
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.xlsx"'
        
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = _("Outstanding Fees")
        
        headers = [
            _('Admission No'), _('Full Name'), _('Class'), _('Section'), 
            _('Primary Parent'), _('Parent Phone'), _('Outstanding Balance')
        ]
        ws.append(headers)
        
        header_font = Font(bold=True)
        currency_format = f'"{self.request.tenant.schoolprofile.currency_symbol if hasattr(self.request.tenant, "schoolprofile") and self.request.tenant.schoolprofile.currency_symbol else "$"}"#,##0.00'

        for col_num, header_title in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=str(header_title)) # Ensure header is string
            cell.font = header_font
            if "Balance" in str(header_title): # Make currency columns wider
                ws.column_dimensions[get_column_letter(col_num)].width = 22
            else:
                ws.column_dimensions[get_column_letter(col_num)].width = 25


        for student in queryset:
            primary_parent = student.parents.filter(is_primary_guardian=True).first()
            if not primary_parent and student.parents.exists():
                primary_parent = student.parents.first()
            
            row_data = [
                student.admission_number,
                student.full_name,
                student.current_class.name if student.current_class else 'N/A',
                student.current_section.name if student.current_section else 'N/A',
                primary_parent.get_full_name() if primary_parent else 'N/A',
                primary_parent.phone_number if primary_parent and primary_parent.phone_number else 'N/A',
                student.outstanding_balance # This is already a Decimal
            ]
            ws.append(row_data)
            # Apply currency format to the last cell
            ws.cell(row=ws.max_row, column=len(headers)).number_format = currency_format
            
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        response.write(output.getvalue())
        return response

    def get_pdf_template_name(self): # Ensure BaseReportViewMixin calls this
        return 'reporting/pdf/outstanding_fees_pdf.html' # Example

    def export_to_pdf(self, queryset_for_export, request, pdf_context=None): # BaseReportViewMixin might call this with context
        # The BaseReportViewMixin should gather context including self.object_list (paginated)
        # For PDF, we want the full_report_queryset.
        queryset = queryset_for_export or self.get_export_queryset()

        # Get common context from the BaseReportViewMixin's get_context_data
        # This is a bit of a dance; ideally BaseReportViewMixin would manage this state better.
        # For simplicity, let's assume we can reconstruct necessary context here.
        from apps.schools.models import SchoolProfile # Import inside method if not top-level
        
        school_profile_instance = None
        currency_symbol = '$'
        with schema_context(self.request.tenant.schema_name):
            school_profile_instance = SchoolProfile.objects.first()
            if school_profile_instance and school_profile_instance.currency_symbol:
                currency_symbol = school_profile_instance.currency_symbol

        filter_data_display = {}
        if hasattr(self, 'filterset') and self.filterset and self.filterset.form.is_valid():
            for name, value in self.filterset.form.cleaned_data.items():
                if value: # Only include active filters
                    field = self.filterset.form.fields[name]
                    if isinstance(field, forms.ModelChoiceField):
                        filter_data_display[field.label] = str(value)
                    elif isinstance(field, forms.ChoiceField):
                        filter_data_display[field.label] = dict(field.choices).get(value)
                    else:
                        filter_data_display[field.label] = value
        
        total_outstanding_all_students = queryset.aggregate(total_sum=Sum('outstanding_balance'))['total_sum'] or Decimal('0.00')

        pdf_context = {
            'report_items': queryset, # Pass the full filtered list
            'school_profile': school_profile_instance,
            'report_title': self.get_report_title(),
            'current_datetime': timezone.now(),
            'school_currency_symbol': currency_symbol,
            'total_outstanding_all_items': total_outstanding_all_students,
            'applied_filters': filter_data_display, # Pass cleaned filter data for display
            'request': request, # For building absolute URIs for images if needed in PDF
        }
        
        from apps.common.utils import render_to_pdf # Your PDF rendering utility
        pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
        
        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"{self.report_code}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            # To display inline:
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            # To force download:
            # response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response
        else:
            logger.error(f"PDF generation failed for {self.report_code} (Outstanding Fees)")
            messages.error(request, _("An error occurred while generating the PDF report. Please try again."))
            # Redirect back to the report page or an error page
            return redirect(request.META.get('HTTP_REFERER', reverse_lazy('reporting:outstanding_fees_report')))


# # --- NEW EXPORT VIEWS ---

# class OutstandingFeesExportCSVView(OutstandingFeesReportView):
#     """
#     A view to handle the CSV export. It inherits the filtering logic.
#     """
#     def get(self, request, *args, **kwargs):
#         # Get the filtered queryset without pagination
#         queryset = self.get_queryset()
        
#         response = HttpResponse(content_type='text/csv')
#         response['Content-Disposition'] = 'attachment; filename="outstanding_fees_report.csv"'

#         writer = csv.writer(response)
#         # Write header row
#         writer.writerow(['Invoice #', 'Student', 'Admission No', 'Class', 'Issue Date', 'Due Date', 'Total Amount', 'Amount Paid', 'Balance Due'])
        
#         # Write data rows
#         for invoice in queryset:
#             writer.writerow([
#                 invoice.invoice_number_display,
#                 invoice.student.full_name,
#                 invoice.student.admission_number,
#                 invoice.student.current_class.name if invoice.student.current_class else '',
#                 invoice.issue_date,
#                 invoice.due_date,
#                 invoice.total_amount,
#                 invoice.amount_paid,
#                 invoice.balance_due,
#             ])
#         return response


# class OutstandingFeesExportPDFView(OutstandingFeesReportView):
#     """
#     A view to handle the PDF export. It also inherits the filtering logic.
#     """
#     def get(self, request, *args, **kwargs):
#         queryset = self.get_queryset()
#         total_outstanding = queryset.aggregate(total=Sum('balance_due'))['total'] or Decimal('0.00')
#
#         context = {
#             'invoices': queryset,
#             'report_title': "Outstanding Fees Report",
#             'school_profile': SchoolProfile.objects.first(),
#             'tenant': request.tenant,
#             'total_outstanding': total_outstanding,
#             'filter_params': self.filter.form.cleaned_data,
#         }
#
#         pdf = render_to_pdf('reporting/pdf/outstanding_fees_pdf.html', context)
#
#         if pdf:
#             response = HttpResponse(pdf, content_type='application/pdf')
#             response['Content-Disposition'] = 'inline; filename="outstanding_fees_report.pdf"'
#             return response
#         return HttpResponse("Error generating PDF.", status=500)

# ==============================================================================
# Collection Report
# ==============================================================================

# D:\school_fees_saas_v2\apps\reporting\views.py

import csv
import openpyxl
from openpyxl.styles import Font
from openpyxl.utils import get_column_letter
from io import BytesIO
import logging

from django.http import HttpResponse, HttpResponseRedirect
from django.urls import reverse_lazy, reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.generic import ListView
from django.db.models import Sum, Count, Q, F # Q for complex lookups
from django.db.models.functions import Coalesce
from django.contrib import messages

from apps.common.mixins import StaffLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin
from apps.payments.models import Payment
from .forms import CollectionReportFilterForm # Ensure this form is well-defined
from apps.common.utils import render_to_pdf

logger = logging.getLogger(__name__)

class CollectionReportView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin, ListView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'reporting.view_collection_report'
    
    model = Payment # Base model for ListView
    template_name = 'reporting/collection_report.html'
    context_object_name = 'payments_on_page' # Paginated list
    paginate_by = 25

    # Attributes for BaseReportViewMixin
    report_code = 'RPT_COLLECTION'
    filter_form_class = CollectionReportFilterForm # Replace with your actual form class
    report_title_text = _("Fee Collection Report")
    
    # Internal attribute to store the full filtered queryset
    _full_filtered_queryset = None

    def get_report_title(self):
        title_text = _("Fee Collection Report")
        # self.form is instantiated in BaseReportViewMixin.get() or this view's get()
        if hasattr(self, 'form') and self.form and self.form.is_bound and self.form.is_valid():
            start_date = self.form.cleaned_data.get('start_date')
            end_date = self.form.cleaned_data.get('end_date')
            # You can add more elements to the title from other filters if desired
            if start_date and end_date:
                title_text += f" (from {start_date.strftime('%d-%b-%Y')} to {end_date.strftime('%d-%b-%Y')})"
            elif start_date:
                title_text += f" (from {start_date.strftime('%d-%b-%Y')})"
            elif end_date:
                title_text += f" (to {end_date.strftime('%d-%b-%Y')})"
        return title_text
    
    def get_filter_form_initial_data(self): # Called by BaseReportViewMixin's get_filter_form_kwargs
        # Set a wider default date range to include more payments
        today = timezone.now().date()
        # Start from 3 months ago to capture more payment data
        start_date = today.replace(day=1) - timezone.timedelta(days=90)
        return {
            'start_date': start_date,
            'end_date': today
        }

    def _apply_filters_to_queryset(self, queryset):
        """Helper method to apply filters from self.form to a queryset."""
        if hasattr(self, 'form') and self.form and self.form.is_valid():
            logger.debug(f"{self.__class__.__name__}: Applying filters: {self.form.cleaned_data}")
            cd = self.form.cleaned_data
            if cd.get('start_date'): 
                queryset = queryset.filter(payment_date__date__gte=cd['start_date'])
            if cd.get('end_date'): 
                queryset = queryset.filter(payment_date__date__lte=cd['end_date'])
            if cd.get('student_query'):
                sq = cd['student_query']
                queryset = queryset.filter(
                    Q(student__first_name__icontains=sq) | Q(student__last_name__icontains=sq) |
                    Q(student__admission_number__iexact=sq) |
                    Q(parent_payer__first_name__icontains=sq) | Q(parent_payer__last_name__icontains=sq) |
                    Q(parent_payer__email__iexact=sq)
                )
            if cd.get('payment_method'):
                queryset = queryset.filter(payment_method=cd['payment_method'])
            if cd.get('class_obj'):
                queryset = queryset.filter(student__current_class=cd['class_obj'])
            # Add other filters from CollectionReportFilterForm
        elif hasattr(self, 'form') and self.form and not self.form.is_bound and self.form.initial:
            # Apply initial filters if form is unbound but has initial data
            logger.debug(f"{self.__class__.__name__}: Applying initial filters: {self.form.initial}")
            initial = self.form.initial
            if initial.get('start_date'): queryset = queryset.filter(payment_date__date__gte=initial['start_date'])
            if initial.get('end_date'): queryset = queryset.filter(payment_date__date__lte=initial['end_date'])
            # Apply other initial filters if any
        elif hasattr(self, 'form') and self.form and self.form.is_bound and not self.form.is_valid():
            logger.warning(f"{self.__class__.__name__}: Filter form is bound but invalid. Returning empty queryset. Errors: {self.form.errors}")
            return queryset.none() # Or handle as per your requirement for invalid filters

        return queryset
    
    

    def get_queryset(self):
        """
        Called by ListView for pagination.
        self.form is instantiated in self.get() before this is called by super().get().
        """
        base_queryset = super().get_queryset().select_related( # super().get_queryset() is Payment.objects.all()
            'student__current_class', 'student__current_section', # student is already selected by default if model=Payment
            'payment_method', 'processed_by_staff', 'parent_payer'
        ).prefetch_related('allocations__invoice')
        
        filtered_queryset = self._apply_filters_to_queryset(base_queryset)
        
        # Store the full filtered queryset for aggregations and exports
        self._full_filtered_queryset = filtered_queryset 
        
        return filtered_queryset.order_by('-payment_date', '-id')
  
  
    def get_report_data(self, processed_filter_form=None): # <<<< ADD 'processed_filter_form'
    # Now, inside this method, use the 'processed_filter_form' argument
        if processed_filter_form is None:
            processed_filter_form = self.form
    # to access .cleaned_data (if valid) or .initial data.
    # DO NOT rely on self.form within this specific method if you take it as an argument.

        report_specific_data = {
            'summary_total_collected': Decimal('0.00'),
            'summary_transaction_count': 0,
            'collections_by_method': []
        }
        logger.debug(f"{self.__class__.__name__}.get_report_data received form: {type(processed_filter_form)}, bound: {processed_filter_form.is_bound if processed_filter_form else 'N/A'}, valid: {processed_filter_form.is_valid() if processed_filter_form and processed_filter_form.is_bound else 'N/A'}")

        
        current_form = self.form # Access the form instance set by the mixin's get() method

        # Start with a base queryset for summary calculations
        base_queryset_for_summary = self.model.objects.select_related(
            'student__current_class', 'student__current_section',
            'payment_method', 'recorded_by', 'parent_payer', 
            'invoice', 'invoice__student' 
        )
    
        # Get the full filtered queryset.
        # _get_filtered_queryset should use self.form (which is the same instance as processed_filter_form here)
        queryset_for_summary = self._apply_filters_to_queryset(base_queryset_for_summary)

        if current_form and (current_form.is_valid() or (not current_form.is_bound and current_form.initial)):
            # Check if queryset_for_summary (which could be .none()) has results,
            # or if it's an initial load (unbound form with initial data)
            if queryset_for_summary.exists() or (not current_form.is_bound and current_form.initial):
                summary_aggregates = queryset_for_summary.aggregate(
                    total_collected=Coalesce(Sum('amount'), Decimal('0.00')),
                    transaction_count=Count('id')
                )
                report_specific_data['summary_total_collected'] = summary_aggregates.get('total_collected')
                report_specific_data['summary_transaction_count'] = summary_aggregates.get('transaction_count')

                report_specific_data['collections_by_method'] = list(
                    queryset_for_summary.values(method_name=F('payment_method__name'))
                    .annotate(total=Coalesce(Sum('amount'), Decimal('0.00')), count=Count('id'))
                    .order_by('-total')
                    .filter(method_name__isnull=False)
                )
            # else: defaults initialized above will be used if queryset is empty due to invalid filters
        else:
            logger.info(f"{self.__class__.__name__}.get_report_data: Form invalid or not present for summary calculation.")
            # Defaults initialized above will be used.
            
        logger.debug(f"{self.__class__.__name__}.get_report_data returning: {report_specific_data}")
        return report_specific_data


    def get_context_data(self, **kwargs):
        # self.form is set in get().
        # BaseReportViewMixin.get_context_data (called by super()) will:
        #  - Add self.form as 'filter_form'.
        #  - Call self.get_report_data() and merge its results.
        #  - Add 'report_title', 'report_code', 'report_generated_at'.
        # ListView.get_context_data (called by super()) will add paginated 'payments_on_page'.
        context = super().get_context_data(**kwargs)
        
        # Add date filters to context for display in template header, if form was valid
        if hasattr(self, 'form') and self.form and self.form.is_bound and self.form.is_valid():
            context['start_date_filter'] = self.form.cleaned_data.get('start_date')
            context['end_date_filter'] = self.form.cleaned_data.get('end_date')
        elif hasattr(self, 'form') and self.form and not self.form.is_bound and self.form.initial: # For initial load
            context['start_date_filter'] = self.form.initial.get('start_date')
            context['end_date_filter'] = self.form.initial.get('end_date')
            
        logger.debug(f"{self.__class__.__name__}.get_context_data final keys: {list(context.keys())}")
        return context
    
    # --- Export Methods ---
    def get_export_queryset(self): # Called by BaseReportViewMixin's export handling
        # Ensure self._full_filtered_queryset is available
        if not hasattr(self, '_full_filtered_queryset'):
            # This might happen if export is called without get_queryset running first (e.g. direct link)
            # So, we need to robustly generate it.
            logger.warning(f"{self.__class__.__name__}: _full_filtered_queryset not set. Re-running get_queryset logic for export.")
            # Instantiate form if it wasn't (e.g. if get() wasn't called)
            if not hasattr(self, 'form') or not self.form:
                 self.form = self.filter_form_class(self.request.GET or None, **self.get_filter_form_kwargs())

            base_queryset = Payment.objects.select_related( # Use Payment as it's self.model
                'student__current_class', 'student__current_section',
                'payment_method', 'processed_by_staff', 'parent_payer'
            ).prefetch_related('allocations__invoice')
            self._full_filtered_queryset = self._apply_filters_to_queryset(base_queryset)

        return self._full_filtered_queryset.order_by('-payment_date', '-id')


    def export_to_csv(self, queryset_for_export, request): # queryset is from get_export_queryset
        queryset = queryset_for_export or self.get_export_queryset()
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.csv"'
        writer = csv.writer(response)
        headers = ['Date', 'Receipt No', 'Student Name', 'Adm. No', 'Class', 'Invoice No', 'Amount Paid', 'Method', 'Received By', 'Parent Payer']
        writer.writerow(headers)
        for p in queryset:
            writer.writerow([
                p.payment_date.strftime('%Y-%m-%d %H:%M') if p.payment_date else '',
                p.receipt_number or f"PK{p.pk}",
                p.student.get_full_name() if p.student else (p.invoice.student.get_full_name() if p.invoice and p.invoice.student else 'N/A'),
                p.student.admission_number if p.student else (p.invoice.student.admission_number if p.invoice and p.invoice.student else 'N/A'),
                str(p.student.current_class) if p.student and p.student.current_class else (str(p.invoice.student.current_class) if p.invoice and p.invoice.student and p.invoice.student.current_class else 'N/A'),
                p.invoice.invoice_number if p.invoice else _('N/A (Direct)'),
                f"{p.amount:.2f}",
                p.payment_method.name if p.payment_method else _('N/A'),
                p.recorded_by.get_full_name() if p.recorded_by else (getattr(p, 'processed_by_staff', None).get_full_name() if hasattr(p, 'processed_by_staff') and p.processed_by_staff else _('System/N/A')),
                p.parent_payer.get_full_name() if p.parent_payer else _('N/A')
            ])
        return response

    def export_to_excel(self, queryset_for_export, request): # queryset is from get_export_queryset
        queryset = queryset_for_export or self.get_export_queryset()
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.xlsx"'
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = self.get_report_title()[:30]
        
        headers = ['Date', 'Receipt No', 'Student Name', 'Adm. No', 'Class', 'Invoice No', 'Amount Paid', 'Method', 'Recorded By', 'Parent Payer']
        ws.append(headers)
        header_font = Font(bold=True)
        currency_format = '#,##0.00'
        date_format = 'yyyy-mm-dd hh:mm' # Excel format
        
        column_widths = {'A': 20, 'B': 15, 'C': 25, 'D': 15, 'E': 20, 'F': 15, 'G': 15, 'H': 20, 'I': 25, 'J': 25}
        for col_letter, width in column_widths.items():
            ws.column_dimensions[col_letter].width = width
        for cell in ws[1]: cell.font = header_font

        for p in queryset:
            ws.append([
                p.payment_date,
                p.receipt_number or f"PK{p.pk}",
                p.student.get_full_name() if p.student else (p.invoice.student.get_full_name() if p.invoice and p.invoice.student else 'N/A'),
                p.student.admission_number if p.student else (p.invoice.student.admission_number if p.invoice and p.invoice.student else 'N/A'),
                str(p.student.current_class) if p.student and p.student.current_class else (str(p.invoice.student.current_class) if p.invoice and p.invoice.student and p.invoice.student.current_class else 'N/A'),
                p.invoice.invoice_number if p.invoice else _('N/A (Direct)'),
                p.amount,
                p.payment_method.name if p.payment_method else _('N/A'),
                p.recorded_by.get_full_name() if p.recorded_by else (getattr(p, 'processed_by_staff', None).get_full_name() if hasattr(p, 'processed_by_staff') and p.processed_by_staff else _('System/N/A')),
                p.parent_payer.get_full_name() if p.parent_payer else _('N/A')
            ])
            ws.cell(row=ws.max_row, column=1).number_format = date_format # Apply date format
            ws.cell(row=ws.max_row, column=7).number_format = currency_format # Amount Paid

        output = BytesIO()
        wb.save(output)
        output.seek(0)
        response.write(output.getvalue())
        return response
        
    def get_pdf_template_name(self):
        return f"reporting/pdf/{self.report_code}_pdf.html" # e.g. reporting/pdf/collection_report_pdf.html

    def export_to_pdf(self, queryset_for_export, request, pdf_context=None): # queryset & pdf_context from BaseReportViewMixin.get()
        queryset = queryset_for_export or self.get_export_queryset()
        # pdf_context already contains 'report_items': queryset, 'report_title', etc. from BaseReportViewMixin
        # We might want to add the specific summary data from this view's get_report_data
        if pdf_context is None:
            pdf_context = {}
        report_data_summaries = self.get_report_data() # Call it to get summaries
        pdf_context.update(report_data_summaries) # Add summaries like 'summary_total_collected'
        pdf_context['report_items'] = queryset

        pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"{self.report_code}_{timezone.now().strftime('%Y%m%d')}.pdf"
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            return response
        else:
            logger.error(f"PDF generation failed for {self.report_code} ({self.get_report_title()})")
            messages.error(request, _("Sorry, we could not generate the PDF report at this time."))
            # Redirect back to the HTML report page, preserving filters
            query_params = request.GET.copy(); query_params.pop('export', None)
            return HttpResponseRedirect(f"{request.path}?{query_params.urlencode()}")


# ==============================================================================
# Trial Balance Report
# # ==============================================================================

# apps/reporting/views.py
import logging
from decimal import Decimal
from django.utils import timezone
from django.views.generic import TemplateView
from django.urls import reverse_lazy # Not strictly needed if set in Base
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib import messages # For user feedback if needed
from django.db.models import Sum, Q, F
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _


# Models (ensure all are correctly imported)
from apps.accounting.models import Account as ChartOfAccount, AccountType, JournalEntryItem # Using JournalEntryItem

# Forms
from .forms import ReportPeriodForm # Your filter form for this report


from django.utils import timezone
from django.db.models import Sum, F, Q, DecimalField, Value # Value not used here yet
from django.db.models.functions import Coalesce
from decimal import Decimal
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponse, HttpResponseRedirect # Added HttpResponseRedirect
from django.shortcuts import redirect # Added redirect

# Your model imports: ChartOfAccount, JournalEntry, JournalEntryItem, AccountType, SchoolProfile
from apps.accounting.models import Account, JournalEntry, JournalEntryItem, AccountType 
from apps.schools.models import SchoolProfile # Assuming SchoolProfile is here

# Your form import:
from .forms import ReportPeriodForm 
# Your mixin import:
from apps.common.mixins import TenantPermissionRequiredMixin # Assuming BaseReportViewMixin is also there or imported by it
from apps.common.mixins import BaseReportViewMixin # Explicitly if BaseReportViewMixin is separate

# Your PDF utility:
from apps.common.utils import render_to_pdf 

# For Excel/CSV
import csv
import openpyxl # Ensure openpyxl is installed: pip install openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, Alignment
from io import BytesIO

import logging
logger = logging.getLogger(__name__)


class TrialBalanceView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    permission_required = 'reporting.view_trial_balance_report' # Define your actual permission
    template_name = 'reporting/trial_balance_report.html'

    report_code = 'TB001' # Example report code
    filter_form_class = ReportPeriodForm # This is used by BaseReportViewMixin

    # report_title is now a property that uses the processed filter form
    @property
    def report_title(self):
        # Access the processed_filter_form which should be set by BaseReportViewMixin's get method
        form = getattr(self, 'processed_filter_form', None)
        report_date_val = None
        if form and form.is_bound and form.is_valid():
            report_date_val = form.cleaned_data.get('report_date')
        elif form and not form.is_bound and form.initial: # Unbound form, use initial
            report_date_val = form.initial.get('report_date')
        
        if report_date_val:
            return _(f"Trial Balance as of {report_date_val.strftime('%d %B %Y')}")
        return _("Trial Balance")
    
    # get_filter_form_initial_data is called by BaseReportViewMixin
    def get_filter_form_initial_data(self):
        return {'report_date': timezone.localdate()}

    def get_report_data(self, filter_form=None): # <<<< CORRECTED SIGNATURE
    # Now, inside this method, use 'self.form' to access the filter form instance,
    # its cleaned_data, or initial data.
    # self.form was set by BaseReportViewMixin.get()

        report_specific_data = {
            'accounts_data': [],
            'total_debits': Decimal('0.00'),
            'total_credits': Decimal('0.00'),
            'report_date': None, # Or a default
        }
        logger.debug(f"{self.__class__.__name__}.get_report_data called. self.form type: {type(self.form)}, bound: {self.form.is_bound if self.form else 'N/A'}, valid: {self.form.is_valid() if self.form and self.form.is_bound else 'N/A'}")

        current_form = self.form # For clarity

        if current_form and (current_form.is_valid() or (not current_form.is_bound and current_form.initial)):
            report_date = None
            if current_form.is_valid(): # Form was bound and valid
                report_date = current_form.cleaned_data.get('report_date')
                logger.debug(f"{self.__class__.__name__}: Using report_date from valid form: {report_date}")
            elif not current_form.is_bound and current_form.initial: # Form unbound, use initial
                report_date = current_form.initial.get('report_date')
                logger.debug(f"{self.__class__.__name__}: Using report_date from initial data: {report_date}")

            if not report_date: # Fallback if still no date
                report_date = timezone.now().date()
                logger.debug(f"{self.__class__.__name__}: report_date fell back to today: {report_date}")
            
            report_specific_data['report_date'] = report_date

            # --- Actual data fetching for Trial Balance using report_date ---
            from apps.accounting.models import Account

            # Get all active accounts with their balances as of the report date
            accounts = Account.objects.filter(is_active=True).select_related('account_type').order_by('code', 'name')

            trial_balance_data = []
            total_debits = Decimal('0.00')
            total_credits = Decimal('0.00')

            for account in accounts:
                # Get account balance as of the report date
                balance = account.get_balance(as_of_date=report_date)

                if balance != Decimal('0.00'):  # Only include accounts with non-zero balances
                    debit_balance = Decimal('0.00')
                    credit_balance = Decimal('0.00')

                    # Determine if balance should be shown as debit or credit
                    if account.account_type and account.account_type.normal_balance == AccountType.NormalBalanceChoices.DEBIT:
                        if balance >= 0:
                            debit_balance = balance
                            total_debits += balance
                        else:
                            credit_balance = abs(balance)
                            total_credits += abs(balance)
                    elif account.account_type and account.account_type.normal_balance == AccountType.NormalBalanceChoices.CREDIT:
                        if balance >= 0:
                            credit_balance = balance
                            total_credits += balance
                        else:
                            debit_balance = abs(balance)
                            total_debits += abs(balance)

                    trial_balance_data.append({
                        'account': account,
                        'balance': balance,
                        'debit_balance': debit_balance,
                        'credit_balance': credit_balance,
                    })

            report_specific_data['trial_balance_data'] = trial_balance_data
            report_specific_data['total_debits'] = total_debits
            report_specific_data['total_credits'] = total_credits
            report_specific_data['balance_difference'] = total_debits - total_credits

            logger.info(f"Trial Balance: {len(trial_balance_data)} accounts, Total Debits: {total_debits}, Total Credits: {total_credits}")

        else:
            logger.warning(f"{self.__class__.__name__}.get_report_data: Form invalid or not present. Errors: {current_form.errors if current_form else 'No Form'}")
            report_specific_data['report_date'] = timezone.now().date() # Default date for display

        return report_specific_data

    # Ensure your export methods are correctly defined within the class
    def export_to_csv(self, report_data_dict, request):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{report_data_dict["report_date"].strftime("%Y%m%d")}.csv"'
        writer = csv.writer(response)
        writer.writerow([self.report_title.upper()]) # Use self.report_title (property)
        writer.writerow([f'As at: {report_data_dict["report_date"].strftime("%d %B %Y")}'])
        writer.writerow([])
        writer.writerow(['Account Code', 'Account Name', 'Debit', 'Credit'])
        for item in report_data_dict['trial_balance_items']: writer.writerow([item['account_code'], item['account_name'], item['debit'], item['credit']])
        writer.writerow([])
        writer.writerow(['', 'TOTALS', report_data_dict['total_debits'], report_data_dict['total_credits']])
        if not report_data_dict['totals_match']: writer.writerow(['', 'DIFFERENCE', report_data_dict['difference'], ''])
        return response

    def export_to_excel(self, report_data_dict, request):
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{report_data_dict["report_date"].strftime("%Y%m%d")}.xlsx"'
        wb = openpyxl.Workbook(); ws = wb.active; ws.title = "Trial Balance"; title_font = Font(bold=True, size=14); header_font = Font(bold=True); currency_format = '#,##0.00'
        ws.merge_cells('A1:D1'); title_cell = ws['A1']; title_cell.value = self.report_title.upper(); title_cell.font = title_font; title_cell.alignment = Alignment(horizontal="center") # Use self.report_title
        ws.merge_cells('A2:D2'); date_cell = ws['A2']; date_cell.value = f'As at: {report_data_dict["report_date"].strftime("%d %B %Y")}'; date_cell.alignment = Alignment(horizontal="center")
        ws.append([]); headers = ['Account Code', 'Account Name', 'Debit', 'Credit']; header_row_num = ws.max_row
        for col_num, header_title in enumerate(headers, 1):
            cell = ws.cell(row=header_row_num, column=col_num, value=header_title); cell.font = header_font
            ws.column_dimensions[get_column_letter(col_num)].width = 35 if col_num == 2 else 18 # Adjusted widths
            if col_num > 2: cell.alignment = Alignment(horizontal="right")
        for item in report_data_dict['trial_balance_items']:
            ws.append([item['account_code'], item['account_name'], item['debit'] if item['debit']!=0 else '', item['credit'] if item['credit']!=0 else ''])
            ws.cell(row=ws.max_row, column=3).number_format = currency_format
            ws.cell(row=ws.max_row, column=4).number_format = currency_format
        ws.append([]); total_row_num = ws.max_row
        ws.cell(row=total_row_num, column=2, value="TOTALS").font = header_font; ws.cell(row=total_row_num, column=2).alignment = Alignment(horizontal="right")
        ws.cell(row=total_row_num, column=3, value=report_data_dict['total_debits']).font = header_font; ws.cell(row=total_row_num, column=3).number_format = currency_format
        ws.cell(row=total_row_num, column=4, value=report_data_dict['total_credits']).font = header_font; ws.cell(row=total_row_num, column=4).number_format = currency_format
        if not report_data_dict['totals_match']:
            ws.append([]); diff_row_num = ws.max_row
            ws.cell(row=diff_row_num, column=2, value="DIFFERENCE").font = Font(bold=True, color="FF0000"); ws.cell(row=diff_row_num, column=2).alignment = Alignment(horizontal="right")
            ws.cell(row=diff_row_num, column=3, value=report_data_dict['difference']).font = Font(bold=True, color="FF0000"); ws.cell(row=diff_row_num, column=3).number_format = currency_format
        output = BytesIO(); wb.save(output); output.seek(0); response.write(output.getvalue())
        return response

    def get_pdf_template_name(self):
        return 'reporting/pdf/trial_balance_pdf.html' # Or your actual PDF template path

    def export_to_pdf(self, report_data_dict, request, html_context_for_pdf):
        # html_context_for_pdf is prepared by BaseReportViewMixin.get_context_data_for_pdf
        pdf = render_to_pdf(self.get_pdf_template_name(), html_context_for_pdf)
        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"{self.report_code}_{report_data_dict['report_date'].strftime('%Y%m%d')}.pdf"
            # Use 'inline' for preview, 'attachment' for direct download
            response['Content-Disposition'] = f'inline; filename="{filename}"' 
            return response
        else:
            logger.error(f"PDF generation failed for {self.report_code} (Trial Balance)")
            messages.error(request, _("Failed to generate PDF report."))
            # Redirect back to the report page without export params
            query_params = request.GET.copy()
            query_params.pop('export', None)
            return redirect(f"{request.path}?{query_params.urlencode()}")


# ==============================================================================
# Income/Expense Report (Profit & Loss)
# ==============================================================================

# apps/reporting/base_views.py (or in views.py if not many reports)
import logging
from django.utils import timezone
from django.views.generic import TemplateView # TemplateView is a good base for reports
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)


# apps/reporting/views.py
# (Keep imports from BaseReportViewMixin section: logging, Decimal, timezone, TemplateView, etc.)
from decimal import Decimal
from django.utils import timezone # Ensure timezone is imported if used for defaults
from django.conf import settings # For ACCOUNTING_..._TYPE_CODE
from django.contrib import messages # For error messages
from django.utils.translation import gettext_lazy as _
from apps.accounting.models import AccountType # Assuming AccountType model is here
# Import your JournalEntryLine or relevant transaction model for _get_balances_by_account_type
# from apps.accounting.models import JournalEntryLine 
import logging

logger = logging.getLogger(__name__)

class IncomeExpenseReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    permission_required = 'reporting.view_income_expense_report' # Specific permission
    template_name = 'reporting/income_expense_report.html'      # Your specific template

    report_code = 'RPT_INCOME_EXPENSE'
    filter_form_class = IncomeExpenseReportForm # The form defined above

    def get_report_title(self): 
        return _("Income Statement (Profit & Loss)") # Use _() for translation
    
    def get_initial_filter_data(self):
        # Default: Previous full calendar month
        today = timezone.localdate()
        first_day_current_month = today.replace(day=1)
        last_day_previous_month = first_day_current_month - timezone.timedelta(days=1)
        first_day_previous_month = last_day_previous_month.replace(day=1)
        return {'start_date': first_day_previous_month, 'end_date': last_day_previous_month}

    def _get_balances_by_account_type(self, account_type_instance, start_date, end_date):
        """ Helper to calculate total balance for accounts of a specific type. """
        items_list, total_balance_for_type = [], Decimal('0.00')
        if not account_type_instance:
            return items_list, total_balance_for_type

        accounts_of_this_type = Account.objects.filter(
            account_type=account_type_instance,
            is_active=True # Only consider active accounts in CoA
        ).order_by('code')

        for acc in accounts_of_this_type:
            # Use the Account model's get_balance method for period calculation
            balance = acc.get_balance(start_date=start_date, as_of_date=end_date)

            if balance != Decimal('0.00'): # Only list accounts with activity in the period
                items_list.append({
                    'account_code': acc.code or 'N/A',
                    'account_name': acc.name,
                    'amount': balance
                })
            total_balance_for_type += balance

        return items_list, total_balance_for_type

    def get_report_data(self, filter_form=None): # Signature corrected
        """
        Fetches and calculates data for the Income & Expense (Profit & Loss) Report.
        Uses self.form (set by BaseReportViewMixin.get()) for filter criteria.
        """
        start_date_filter, end_date_filter = None, None
        
        # Initialize the report_data dictionary with all expected keys and default values
        report_data = {
            'start_date_filter': None, 'end_date_filter': None, # Store the actual dates used for the report
            'revenues': [], 'total_revenue': Decimal('0.00'),
            'cost_of_sales': [], 'total_cost_of_sales': Decimal('0.00'),
            'operating_expenses': [], 'total_operating_expenses': Decimal('0.00'),
            'gross_profit': Decimal('0.00'),
            'operating_income': Decimal('0.00'),
            'other_income_items': [], 'total_other_income': Decimal('0.00'),
            'other_expense_items': [], 'total_other_expenses': Decimal('0.00'),
            'income_before_tax': Decimal('0.00'),
            'tax_expense': Decimal('0.00'),
            'net_profit': Decimal('0.00'),
            'report_generated_successfully': False # Flag to indicate if data fetching was successful
        }

        form_instance = self.form # Access the form instance set by BaseReportViewMixin's get() method

        if form_instance: # Check if a filter form is even being used for this report
            if form_instance.is_valid(): # Form was bound with GET data and is valid
                start_date_filter = form_instance.cleaned_data.get('start_date')
                end_date_filter = form_instance.cleaned_data.get('end_date')
                logger.debug(f"{self.__class__.__name__}: Using valid filter dates: Start={start_date_filter}, End={end_date_filter}")
            elif not form_instance.is_bound and form_instance.initial: # Form is unbound, use its initial data
                start_date_filter = form_instance.initial.get('start_date')
                end_date_filter = form_instance.initial.get('end_date')
                logger.debug(f"{self.__class__.__name__}: Using initial filter dates: Start={start_date_filter}, End={end_date_filter}")
            else: # Form is bound but invalid, or unbound with no initial data relevant for date range
                logger.warning(f"{self.__class__.__name__}.get_report_data: Form is invalid or has no applicable date range. Errors: {form_instance.errors if form_instance.is_bound else 'Unbound with no relevant initial data'}")
                messages.error(self.request, _("Please provide a valid date range for the report."))
                return report_data # Return empty structure with report_generated_successfully = False
        else: # No filter form was defined for this report view
            logger.info(f"{self.__class__.__name__}: No filter form class. Attempting to run report without filters (e.g., for all time or default period).")
            # Decide on default behavior: run for all time? Specific default period? Or error?
            # For an Income Statement, a period is usually required.
            # If you want to enforce filters, this branch might also return report_data.
            # For now, let's assume it tries to run with no date filters, which might be problematic.
            # It's better for the filter form to provide default dates if no GET params are given.
            # The get_filter_form_initial_data() in the view can set these.
            messages.warning(self.request, _("Date range not specified. Report may show all-time data or default period."))
            # Fallback to a very wide range or a default if truly no form is used
            # start_date_filter = very_old_date 
            # end_date_filter = timezone.now().date()

        if not (start_date_filter and end_date_filter):
            logger.error(f"{self.__class__.__name__}: Date range (start_date or end_date) is missing after form processing.")
            messages.error(self.request, _("A valid date range (both start and end date) is required for this report."))
            return report_data # Return empty structure

        report_data.update({
            'start_date_filter': start_date_filter, 
            'end_date_filter': end_date_filter
        })

        try:
            # Get account types by classification instead of relying on settings
            revenue_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.REVENUE)
            expense_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.EXPENSE)
            cogs_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.COGS)

        except Exception as e: # Catch potential errors during AccountType fetching
            logger.error(f"IncomeExpenseReport: Error fetching AccountTypes: {e}", exc_info=True)
            messages.error(self.request, _("An error occurred while loading accounting configurations."))
            return report_data

        # Fetch balances for each classification
        try:
            # Calculate revenue totals
            revenue_items = []
            total_revenue = Decimal('0.00')
            for revenue_type in revenue_types:
                items, total = self._get_balances_by_account_type(revenue_type, start_date_filter, end_date_filter)
                revenue_items.extend(items)
                total_revenue += total

            # Calculate COGS totals
            cogs_items = []
            total_cogs = Decimal('0.00')
            for cogs_type in cogs_types:
                items, total = self._get_balances_by_account_type(cogs_type, start_date_filter, end_date_filter)
                cogs_items.extend(items)
                total_cogs += total

            # Calculate expense totals
            expense_items = []
            total_expenses = Decimal('0.00')
            for expense_type in expense_types:
                items, total = self._get_balances_by_account_type(expense_type, start_date_filter, end_date_filter)
                expense_items.extend(items)
                total_expenses += total

            # Update report data
            report_data['revenues'] = revenue_items
            report_data['total_revenue'] = total_revenue
            report_data['cost_of_sales'] = cogs_items
            report_data['total_cost_of_sales'] = total_cogs
            report_data['operating_expenses'] = expense_items
            report_data['total_operating_expenses'] = total_expenses

        except Exception as e:
            logger.error(f"IncomeExpenseReport: Error in _get_balances_by_account_type: {e}", exc_info=True)
            messages.error(self.request, _("An error occurred while calculating account balances for the report."))
            return report_data # Return partially filled or empty data

        # Calculate derived values
        report_data['gross_profit'] = report_data['total_revenue'] - report_data['total_cost_of_sales']
        report_data['operating_income'] = report_data['gross_profit'] - report_data['total_operating_expenses']
        
        # Further calculations for net profit
        # report_data['income_before_tax'] = report_data['operating_income'] + report_data['total_other_income'] - report_data['total_other_expenses']
        # report_data['net_profit'] = report_data['income_before_tax'] - report_data['tax_expense']
        # For now, simplified:
        report_data['net_profit'] = report_data['operating_income'] # Adjust as you add other income/expense/tax

        report_data['report_generated_successfully'] = True # Set flag
        
        logger.info(
            f"Income & Expense Report generated for period {start_date_filter} to {end_date_filter}. "
            f"Total Revenue: {report_data['total_revenue']}, Net Profit: {report_data['net_profit']}"
        )
        return report_data
        # get_context_data() is inherited from BaseReportViewMixin.
        # It will call get_report_title(), get_filter_form(), and get_report_data(form).


    def export_to_excel(self, report_data_dict, request):
        # ... (Excel export for P&L as you defined before, using report_data_dict) ...
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = f"{self.report_code}_{report_data_dict['start_date'].strftime('%Y%m%d')}_to_{report_data_dict['end_date'].strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb = openpyxl.Workbook(); ws = wb.active; ws.title = "Income Statement"
        title_font=Font(bold=True, size=14); header_font=Font(bold=True,size=12); sub_header_font=Font(bold=True); currency_format='#,##0.00'
        ws.merge_cells('A1:C1'); title_cell=ws['A1']; title_cell.value=self.get_report_title().upper(); title_cell.font=title_font; title_cell.alignment=Alignment(horizontal="center")
        ws.merge_cells('A2:C2'); date_cell=ws['A2']; date_cell.value=f"For period: {report_data_dict['start_date'].strftime('%d %b %Y')} to {report_data_dict['end_date'].strftime('%d %b %Y')} "; date_cell.alignment=Alignment(horizontal="center")
        row = 4
        def write_pl_section(title, items, total_val, total_label="Total"):
            nonlocal row
            ws.cell(row=row, column=1, value=title).font = header_font; row +=1
            for item in items: ws.cell(row=row, column=1, value=f"    {item['account_name']}"); ws.cell(row=row, column=2, value=item['amount']).number_format=currency_format; row +=1
            ws.cell(row=row, column=1, value=total_label).font=sub_header_font; ws.cell(row=row, column=2, value=total_val).font=sub_header_font; ws.cell(row=row, column=2).number_format=currency_format; row +=2
        
        write_pl_section("Revenue", report_data_dict['revenues'], report_data_dict['total_revenue'])
        write_pl_section("Cost of Sales", report_data_dict['cost_of_sales'], report_data_dict['total_cost_of_sales'])
        ws.cell(row=row, column=1, value="GROSS PROFIT").font=header_font; ws.cell(row=row, column=2, value=report_data_dict['gross_profit']).font=header_font; ws.cell(row=row, column=2).number_format=currency_format; row+=2
        write_pl_section("Operating Expenses", report_data_dict['operating_expenses'], report_data_dict['total_operating_expenses'])
        ws.cell(row=row, column=1, value="OPERATING INCOME").font=header_font; ws.cell(row=row, column=2, value=report_data_dict['operating_income']).font=header_font; ws.cell(row=row, column=2).number_format=currency_format; row+=2
        ws.cell(row=row, column=1, value="NET PROFIT / (LOSS)").font=Font(bold=True, size=13); np_cell=ws.cell(row=row, column=2, value=report_data_dict['net_profit']); np_cell.font=Font(bold=True, size=13); np_cell.number_format=currency_format
        ws.column_dimensions['A'].width = 45; ws.column_dimensions['B'].width = 20
        output = BytesIO(); wb.save(output); output.seek(0); response.write(output.getvalue()); return response

    def export_to_pdf(self, report_data_dict, request, html_context): # report_data is dict
        pdf_context = {
            'report_data': report_data_dict, 'school_profile': self.get_school_profile(),
            'report_title': self.get_report_title(), 'current_datetime': timezone.now(),
        }
        pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"{self.report_code}_{report_data_dict['start_date'].strftime('%Y%m%d')}_{report_data_dict['end_date'].strftime('%Y%m%d')}.pdf"
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            return response
        else:
            logger.error(f"PDF generation failed for {self.report_code} (P&L)")
            messages.error(request, "Failed to generate PDF report.")
            return redirect(request.path)


# ==============================================================================
# Expense Report View
# ==============================================================================

# D:\school_fees_saas_v2\apps\reporting\views.py


from django.db.models import Sum, Count, F, Q, Value, CharField, Case, When, TextField, DecimalField
from django.db.models.functions import Coalesce
from decimal import Decimal
from apps.finance.models import Expense, ExpenseCategory # Your Expense model
from .forms import ExpenseReportFilterForm 

# ... (other report views) ...

class ExpenseReportView(StaffLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin, ListView):
    login_url = reverse_lazy('schools:staff_login')
    permission_required = 'reporting.view_expense_report' # Define this permission
    
    model = Expense # Main model for the ListView
    template_name = 'reporting/expense_report.html'
    context_object_name = 'expenses_on_page' # For paginated list
    paginate_by = 30

    # Attributes for BaseReportViewMixin
    report_title_text = _("Expense Report")
    report_code = "RPT_EXPENSE"
    filter_form_class = ExpenseReportFilterForm

    # Internal attribute to store full filtered queryset
    _full_filtered_queryset = None

    def get_filter_form_initial_data(self):
        # Default to current month
        today = timezone.now().date()
        return {
            'start_date': today.replace(day=1),
            'end_date': today
        }
    
    def get_report_data(self, filter_form=None): # Signature corrected
        """
        Calculates summary data for the expense report.
        Uses self.form (set and validated/processed in BaseReportViewMixin.get()).
        """
        report_specific_data = {
            'summary_total_expenses': Decimal('0.00'),
            'summary_expense_count': 0,
            'expenses_by_category': [], # Example summary
            'report_start_date': None, # For displaying in template
            'report_end_date': None,   # For displaying in template
        }
        
        current_form = self.form # Access the form instance set by BaseReportViewMixin's get()

        logger.debug(
            f"{self.__class__.__name__}.get_report_data called. "
            f"self.form type: {type(current_form)}, "
            f"bound: {current_form.is_bound if current_form else 'N/A'}, "
            f"valid: {current_form.is_valid() if current_form and current_form.is_bound else 'N/A'}"
        )

        # Determine date range to use from the form
        start_date_filter = None
        end_date_filter = None

        if current_form: # Check if a form instance exists
            if current_form.is_valid():
                start_date_filter = current_form.cleaned_data.get('start_date')
                end_date_filter = current_form.cleaned_data.get('end_date')
                logger.debug(f"{self.__class__.__name__}: Using filter dates from valid form: Start={start_date_filter}, End={end_date_filter}")
            elif not current_form.is_bound and current_form.initial: # Form is unbound, use its initial values
                start_date_filter = current_form.initial.get('start_date')
                end_date_filter = current_form.initial.get('end_date')
                logger.debug(f"{self.__class__.__name__}: Using initial filter dates: Start={start_date_filter}, End={end_date_filter}")
            # If form is bound but invalid, filters remain None, report will be unfiltered or empty based on get_queryset
            
        report_specific_data['report_start_date'] = start_date_filter
        report_specific_data['report_end_date'] = end_date_filter

        # Get the full filtered queryset for summary calculations
        # _full_filtered_queryset is set by get_queryset calling _apply_filters_to_queryset
        # If get_queryset hasn't run yet (e.g., if BaseReportViewMixin.get() calls this *before* super().get()),
        # then _full_filtered_queryset might not be set.
        # It's safer for get_report_data to use _apply_filters_to_queryset on a base queryset.
        
        base_summary_qs = self.model.objects.all() # Start with Expense.objects.all()
        queryset_for_summary = self._apply_filters_to_queryset(base_summary_qs) # Apply same filters

        if queryset_for_summary.exists() or \
            (current_form and not current_form.is_bound and current_form.initial): # Calculate even for initial load
            
            summary_aggregates = queryset_for_summary.aggregate(
                total_amount=Coalesce(Sum('amount'), Decimal('0.00')),
                count=Count('id')
            )
            report_specific_data['summary_total_expenses'] = summary_aggregates.get('total_amount')
            report_specific_data['summary_expense_count'] = summary_aggregates.get('count')

            # Example: Expenses grouped by category
            report_specific_data['expenses_by_category'] = list(
                queryset_for_summary
                .values('category__name') # Assuming Expense model has FK to ExpenseCategory
                .annotate(total=Sum('amount'), count_items=Count('id'))
                .order_by('-total')
                .filter(category__name__isnull=False) # Exclude uncategorized if category can be null
            )
            logger.info(f"{self.__class__.__name__}: Summarized expenses. Total: {report_specific_data['summary_total_expenses']}")
        else:
            logger.info(f"{self.__class__.__name__}.get_report_data: No data after filtering or form invalid for summary.")
            # Defaults are already set in report_specific_data

        return report_specific_data

    def _apply_filters_to_queryset(self, queryset):
        # self.form is instantiated in BaseReportViewMixin.get()
        
        # Check if self.form exists before trying to access its attributes
        if not hasattr(self, 'form') or self.form is None:
            logger.warning(f"{self.__class__.__name__}._apply_filters_to_queryset: self.form is not set. No filters will be applied.")
            return queryset # Return the original queryset if no form is available

        current_form = self.form # Use a local variable for clarity

        if current_form.is_valid(): # Form was bound with GET data and is valid
            logger.debug(f"{self.__class__.__name__}._apply_filters_to_queryset: Applying filters from cleaned_data: {current_form.cleaned_data}")
            cd = current_form.cleaned_data
            if cd.get('start_date'):
                queryset = queryset.filter(expense_date__gte=cd['start_date'])
            if cd.get('end_date'):
                queryset = queryset.filter(expense_date__lte=cd['end_date'])
            if cd.get('category'):
                queryset = queryset.filter(category=cd['category'])
            if cd.get('payment_method'):
                queryset = queryset.filter(payment_method=cd['payment_method'])
            if cd.get('description_contains'):
                queryset = queryset.filter(description__icontains=cd['description_contains'])
            # Add other filters based on your ExpenseReportFilterForm fields
        
        elif not current_form.is_bound and current_form.initial: # Form is unbound, use its initial values
            logger.debug(f"{self.__class__.__name__}._apply_filters_to_queryset: Applying initial filters: {current_form.initial}")
            initial = current_form.initial
            if initial.get('start_date'): 
                queryset = queryset.filter(expense_date__gte=initial['start_date'])
            if initial.get('end_date'): 
                queryset = queryset.filter(expense_date__lte=initial['end_date'])
            if initial.get('category'): 
                queryset = queryset.filter(category=initial['category'])
            if initial.get('payment_method'): 
                queryset = queryset.filter(payment_method=initial['payment_method'])
            if initial.get('description_contains'):
                # Typically, you wouldn't filter by description_contains on initial load
                # unless initial['description_contains'] has a value.
                pass
            # Add other initial filters if applicable
            
        elif current_form.is_bound and not current_form.is_valid(): # Form is bound but invalid
            logger.warning(f"{self.__class__.__name__}._apply_filters_to_queryset: Filter form is bound but invalid. Returning empty queryset. Errors: {current_form.errors}")
            return queryset.none() # Return an empty queryset for invalid filters
        
        # If form is unbound and has no initial data, or if filters didn't match anything,
        # the original (or partially filtered) queryset is returned.
        return queryset # <<< CORRECT: Return the (potentially modified) queryset
    
    
    def get_export_queryset(self):
        base_queryset = self.model.objects.select_related(
            'category', 'payment_method', 'recorded_by', 'linked_account'
        )
        return self._apply_filters_to_queryset(base_queryset).order_by('-expense_date', '-id')

    # --- Implement Export Methods (CSV, Excel, PDF) ---
    def export_to_csv(self, queryset_for_export, request):
        queryset = queryset_for_export or self.get_export_queryset()
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.csv"'
        writer = csv.writer(response)
        headers = ['Date', 'Description', 'Category', 'Amount', 'Payment Method', 'Recorded By']
        writer.writerow(headers)
        for ex in queryset:
            writer.writerow([
                ex.expense_date.strftime('%Y-%m-%d') if ex.expense_date else '',
                ex.description,
                ex.category.name if ex.category else 'N/A',
                f"{ex.amount:.2f}",
                ex.payment_method.name if ex.payment_method else 'N/A',
                ex.recorded_by.get_full_name() if ex.recorded_by else 'N/A'
            ])
        return response

    def export_to_excel(self, queryset_for_export, request):
        queryset = queryset_for_export or self.get_export_queryset()
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename="{self.report_code}_{timezone.now().strftime("%Y%m%d")}.xlsx"'
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = self.get_report_title()[:30]
        headers = ['Date', 'Description', 'Category', 'Amount', 'Payment Method', 'Recorded By']
        ws.append(headers)
        # ... (Apply styling and formatting as in CollectionReportView) ...
        header_font = Font(bold=True); currency_format = '#,##0.00'; date_format = 'yyyy-mm-dd'
        for cell in ws[1]: cell.font = header_font
        # Basic width, adjust as needed
        for col_letter in ['A', 'B', 'C', 'D', 'E', 'F']: ws.column_dimensions[col_letter].width = 20

        for ex in queryset:
            ws.append([
                ex.expense_date,
                ex.description,
                ex.category.name if ex.category else 'N/A',
                ex.amount,
                ex.payment_method.name if ex.payment_method else 'N/A',
                ex.recorded_by.get_full_name() if ex.recorded_by else 'N/A'
            ])
            ws.cell(row=ws.max_row, column=1).number_format = date_format
            ws.cell(row=ws.max_row, column=4).number_format = currency_format
        
        output = BytesIO(); wb.save(output); output.seek(0)
        response.write(output.getvalue())
        return response

    def get_pdf_template_name(self):
        return f"reporting/pdf/{self.report_code}_pdf.html" # e.g., reporting/pdf/expense_report_pdf.html

    def export_to_pdf(self, queryset_for_export, request, pdf_context=None): # pdf_context from BaseReportViewMixin
        queryset = queryset_for_export or self.get_export_queryset()
        if pdf_context is None:
            pdf_context = {}
        pdf_context['report_items'] = queryset
        # from apps.common.utils import render_to_pdf # Ensure imported at top of views.py
        pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"{self.report_code}_{timezone.now().strftime('%Y%m%d')}.pdf"
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            return response
        else:
            logger.error(f"PDF generation failed for {self.report_code} ({self.get_report_title()})")
            messages.error(request, _("Sorry, we could not generate the PDF report."))
            query_params = request.GET.copy(); query_params.pop('export', None)
            return HttpResponseRedirect(f"{request.path}?{query_params.urlencode()}")


# ==============================================================================
# Balance Sheet Report
# ==============================================================================
# D:\school_fees_saas_V2\apps\reporting\views.py

from apps.accounting.models import AccountType, Account, GeneralLedger # Ensure these are imported

class BalanceSheetView(LoginRequiredMixin, PermissionRequiredMixin, FormMixin, TemplateView):
    template_name = 'reporting/balance_sheet.html'
    form_class = BalanceSheetFilterForm
    permission_required = 'reporting.view_balance_sheet_report' # Or 'accounting.view_account' # Example, adjust as needed
    # Or a more specific reporting permission like 'reporting.view_balance_sheet'
    login_url = '/portal/staff/login/' # Or reverse_lazy('schools:staff_login')

    # ... (dispatch method can remain as is if you're just adding get_report_data) ...
    # If your dispatch method from the traceback is the one you want to keep, ensure it calls super()
    def dispatch(self, request, *args, **kwargs):
        # Your custom dispatch logic, e.g., initializing form from GET
        if not self.has_permission(): # From PermissionRequiredMixin
            return self.handle_no_permission()
        self.form = self.get_form(self.form_class) # Initialize form
        return super().dispatch(request, *args, **kwargs)


    def get_initial(self):
        return {'as_at_date': timezone.now().date()}

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if self.request.method == 'GET':
            kwargs['data'] = self.request.GET or None # Populate form with GET data
        return kwargs

    def get_report_data(self, form):
        as_at_date = form.cleaned_data.get('as_at_date') if form.is_valid() else timezone.now().date()

        # Get account types by classification instead of relying on settings
        try:
            asset_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.ASSET)
            liability_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.LIABILITY)
            equity_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.EQUITY)
            revenue_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.REVENUE)
            expense_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.EXPENSE)
            cogs_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.COGS)
        except Exception as e:
            messages.error(self.request, f"An error occurred while fetching account types: {e}")
            return {'error': str(e)}

        report_data = {
            'assets': {'accounts': [], 'total_assets': Decimal('0.00')},
            'liabilities': {'accounts': [], 'total_liabilities': Decimal('0.00')},
            'equity': {'accounts': [], 'total_equity': Decimal('0.00')},
            'total_liabilities_and_equity': Decimal('0.00'),
            'net_income_for_period': Decimal('0.00'),
            'as_at_date': as_at_date,
            'error': None,
        }

        # Calculate Net Income for the current financial year (simplified)
        # Get financial year start (assuming January 1st for simplicity)
        current_year = as_at_date.year
        financial_year_start = timezone.datetime(current_year, 1, 1).date()

        # Calculate total revenue
        total_revenue = Decimal('0.00')
        for revenue_type in revenue_types:
            for account in Account.objects.filter(account_type=revenue_type, is_active=True):
                balance = account.get_balance(start_date=financial_year_start, as_of_date=as_at_date)
                total_revenue += balance

        # Calculate total expenses and COGS
        total_expenses = Decimal('0.00')
        for expense_type in expense_types:
            for account in Account.objects.filter(account_type=expense_type, is_active=True):
                balance = account.get_balance(start_date=financial_year_start, as_of_date=as_at_date)
                total_expenses += balance

        for cogs_type in cogs_types:
            for account in Account.objects.filter(account_type=cogs_type, is_active=True):
                balance = account.get_balance(start_date=financial_year_start, as_of_date=as_at_date)
                total_expenses += balance

        # Net Income = Revenue - Expenses (Revenue is credit balance, Expenses are debit balances)
        net_income_for_period = total_revenue - total_expenses
        report_data['net_income_for_period'] = net_income_for_period
        
        # Populate Assets
        for asset_type in asset_types:
            for account in Account.objects.filter(account_type=asset_type, is_active=True):
                balance = account.get_balance(as_of_date=as_at_date)
                if balance != Decimal('0.00'):  # Only include accounts with non-zero balances
                    report_data['assets']['accounts'].append({
                        'code': account.code or 'N/A',
                        'name': account.name,
                        'balance': balance,
                        'type': asset_type.name
                    })
                    report_data['assets']['total_assets'] += balance

        # Populate Liabilities
        for liability_type in liability_types:
            for account in Account.objects.filter(account_type=liability_type, is_active=True):
                balance = account.get_balance(as_of_date=as_at_date)
                if balance != Decimal('0.00'):  # Only include accounts with non-zero balances
                    report_data['liabilities']['accounts'].append({
                        'code': account.code or 'N/A',
                        'name': account.name,
                        'balance': balance,
                        'type': liability_type.name
                    })
                    report_data['liabilities']['total_liabilities'] += balance

        # Populate Equity
        for equity_type in equity_types:
            for account in Account.objects.filter(account_type=equity_type, is_active=True):
                balance = account.get_balance(as_of_date=as_at_date)
                if balance != Decimal('0.00'):  # Only include accounts with non-zero balances
                    report_data['equity']['accounts'].append({
                        'code': account.code or 'N/A',
                        'name': account.name,
                        'balance': balance,
                        'type': equity_type.name
                    })
                    report_data['equity']['total_equity'] += balance

        # Add current period's net income to equity
        if net_income_for_period != Decimal('0.00'):
            report_data['equity']['accounts'].append({
                'code': 'NET_INC',
                'name': 'Net Income (Current Period)',
                'balance': net_income_for_period,
                'type': 'Calculated'
            })
            report_data['equity']['total_equity'] += net_income_for_period
        
        report_data['total_liabilities_and_equity'] = report_data['liabilities']['total_liabilities'] + report_data['equity']['total_equity']

        # Verification: Assets = Liabilities + Equity
        report_data['balance_check'] = report_data['assets']['total_assets'] - report_data['total_liabilities_and_equity']
        
        return report_data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs) # This calls get_form() from FormMixin
        form = context['form'] # Form is now initialized by FormMixin or our dispatch
        
        report_data_dict = {}
        if form.is_valid():
            report_data_dict = self.get_report_data(form)
        elif self.request.method == 'GET' and not self.request.GET: # Initial load
            # Create a default form for initial display
            initial_form_data = self.get_initial()
            default_form = self.form_class(initial=initial_form_data)
            if default_form.is_valid(): # Should be valid with initial data
                report_data_dict = self.get_report_data(default_form)
            else: # Should not happen with just initial data
                report_data_dict['error'] = "Initial form data is invalid."
                for field, errors in default_form.errors.items():
                    for error in errors:
                        messages.error(self.request, f"Form error in {field}: {error}")

        context.update(report_data_dict)
        context['view_title'] = "Balance Sheet"
        context['form'] = form # Ensure form is in context for template rendering
        return context

# Utility function (place in common.utils or reporting.utils)
def get_financial_year_start(date_obj):
    # This is a placeholder. Your actual financial year logic might be tenant-configurable.
    # Assuming financial year starts April 1st for this example.
    if date_obj.month < 4: # Jan, Feb, Mar belong to previous financial year
        return timezone.datetime(date_obj.year - 1, 4, 1).date()
    else: # Apr - Dec belong to current financial year
        return timezone.datetime(date_obj.year, 4, 1).date()


# ==============================================================================
# Cash Flow Statement
# ==============================================================================

# apps/reporting/views.py
import logging
from decimal import Decimal
from django.utils import timezone
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib import messages
from django.db.models import Sum, Q
from django.db.models.functions import Coalesce
from django.conf import settings # For settings.ACCOUNTING_..._CODE

# Models (ensure all are correctly imported)
from apps.accounting.models import AccountType, Account as ChartOfAccount, JournalEntryItem # Using JournalEntryItem
# from apps.schools.models import AcademicYear # Not directly used in this report's filter data

# Forms (your filter form for this report)
from .forms import IncomeExpenseReportForm # Reusing this form for date range

logger = logging.getLogger(__name__)


# D:\school_fees_saas_v2\apps\reporting\views.py

from django.conf import settings # For ACCOUNTING_CASH_TYPE_CODE etc.
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.utils import timezone
from django.db.models import Sum, F, Q, DecimalField, Value # Value might not be used here
from django.db.models.functions import Coalesce
from decimal import Decimal
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponse, HttpResponseRedirect # Added HttpResponseRedirect
from django.shortcuts import redirect

# Your model imports
from apps.accounting.models import Account, JournalEntry, JournalEntryItem, AccountType 
from apps.schools.models import SchoolProfile

# Your form import
from .forms import IncomeExpenseReportForm # Using this for date range

# Your mixin imports
from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin # Ensure both are imported

# Your PDF utility
from apps.common.utils import render_to_pdf 

# For Excel/CSV
import csv
import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, Alignment
from io import BytesIO

import logging
logger = logging.getLogger(__name__)


class CashFlowStatementView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    # login_url = reverse_lazy('schools:staff_login') # Handled by TenantPermissionRequiredMixin if it inherits from LoginRequiredMixin
    permission_required = 'reporting.view_cash_flow_statement' # Make sure this perm exists or create it
    template_name = 'reporting/cash_flow_statement_report.html'

    report_code = 'cash_flow_statement_report'
    filter_form_class = IncomeExpenseReportForm # This will be used by BaseReportViewMixin

    report_code = 'RPT_CASH_FLOW'
    filter_form_class = DateRangeForm # Example
    report_title_text = _("Cash Flow Statement Analysis")

    # get_report_title is a hook called by BaseReportViewMixin
    @property
    def report_title(self): # Changed to @property for dynamic title based on processed form
        form = getattr(self, 'processed_filter_form', None) # Access form set by BaseReportViewMixin
        start_date_val, end_date_val = None, None
        if form:
            if form.is_bound and form.is_valid():
                start_date_val = form.cleaned_data.get('start_date')
                end_date_val = form.cleaned_data.get('end_date')
            elif not form.is_bound and form.initial: # Unbound form with initial data
                start_date_val = form.initial.get('start_date')
                end_date_val = form.initial.get('end_date')

        if start_date_val and end_date_val:
            return _(f"Cash Flow Statement from {start_date_val.strftime('%d %b %Y')} to {end_date_val.strftime('%d %b %Y')}")
        return _("Cash Flow Statement")
    
    # get_filter_form_initial_data is a hook called by BaseReportViewMixin
    def get_filter_form_initial_data(self):
        today = timezone.localdate()
        first_day_current_month = today.replace(day=1)
        last_day_previous_month = first_day_current_month - timezone.timedelta(days=1)
        first_day_previous_month = last_day_previous_month.replace(day=1)
        return {'start_date': first_day_previous_month, 'end_date': last_day_previous_month}

    # get_report_data is the main hook called by BaseReportViewMixin
    def get_report_data(self, filter_form=None): # Signature corrected
        """
        Fetches and calculates data for the Cash Flow Statement.
        Uses self.form (set by BaseReportViewMixin.get()) for filter criteria.
        """
        start_date_filter, end_date_filter = None, None
        
        report_data = {
            'start_date_filter': None, 'end_date_filter': None, # Store actual dates used
            'beginning_cash_balance': Decimal('0.00'),
            'ending_cash_balance': Decimal('0.00'),
            'net_change_in_cash': Decimal('0.00'),
            'operating_activities_items': [], 'net_cash_from_operating': Decimal('0.00'),
            'investing_activities_items': [], 'net_cash_from_investing': Decimal('0.00'),
            'financing_activities_items': [], 'net_cash_from_financing': Decimal('0.00'),
            'report_generated_successfully': False,
            'filter_form_errors': None
        }

        current_form = self.form # Access the form instance set by BaseReportViewMixin's get() method

        logger.debug(
            f"{self.__class__.__name__}.get_report_data called. "
            f"self.form type: {type(current_form)}, "
            f"bound: {current_form.is_bound if current_form else 'N/A'}, "
            f"valid: {current_form.is_valid() if current_form and current_form.is_bound else 'N/A'}"
        )

        if current_form:
            if current_form.is_valid(): # Form was bound with GET data and is valid
                start_date_filter = current_form.cleaned_data.get('start_date')
                end_date_filter = current_form.cleaned_data.get('end_date')
                logger.debug(f"{self.__class__.__name__}: Using valid filter dates: Start={start_date_filter}, End={end_date_filter}")
            elif not current_form.is_bound and current_form.initial: # Form is unbound, use its initial data
                start_date_filter = current_form.initial.get('start_date')
                end_date_filter = current_form.initial.get('end_date')
                logger.debug(f"{self.__class__.__name__}: Using initial filter dates: Start={start_date_filter}, End={end_date_filter}")
            else: # Form is bound but invalid
                report_data['filter_form_errors'] = current_form.errors
                logger.warning(f"{self.__class__.__name__}: Filter form invalid. Not generating report data. Errors: {current_form.errors.as_json() if current_form else 'N/A'}")
                messages.error(self.request, _("Please correct the filter errors to generate the report."))
                return report_data # Return with report_generated_successfully = False
        else: # No filter form was defined for this view
            logger.warning(f"{self.__class__.__name__}: No filter form provided. Cannot generate cash flow statement without a date range.")
            messages.error(self.request, _("Date range filters are required for the Cash Flow Statement."))
            return report_data

        if not (start_date_filter and end_date_filter):
            logger.info(f"{self.__class__.__name__}: Start or end date missing after form processing. Not generating report data.")
            messages.error(self.request, _("A valid date range (both start and end date) is required for this report."))
            return report_data

        report_data.update({
            'start_date_filter': start_date_filter, 
            'end_date_filter': end_date_filter
        })
        logger.info(f"Attempting to generate Cash Flow Statement from {start_date_filter} to {end_date_filter}")

        try:
            # Get cash and bank account types by classification
            asset_types = AccountType.objects.filter(classification=AccountType.ClassificationChoices.ASSET)

            # Filter for cash and bank accounts (typically have 'cash' or 'bank' in name)
            cash_and_bank_accounts = Account.objects.filter(
                account_type__in=asset_types,
                is_active=True
            ).filter(
                Q(name__icontains='cash') | Q(name__icontains='bank') | Q(code__startswith='1000')
            )

            if not cash_and_bank_accounts.exists():
                messages.warning(self.request, _("No active Cash or Bank accounts found in your Chart of Accounts. Cannot generate Cash Flow Statement."))
                return report_data

        except Exception as e:
            logger.error(f"Cash Flow: Error fetching cash/bank accounts: {e}", exc_info=True)
            messages.error(self.request, _("An error occurred while loading accounting configurations for cash flow."))
            return report_data
    

        # Calculate beginning and ending cash balances
        beginning_cash_balance = Decimal('0.00')
        ending_cash_balance = Decimal('0.00')

        for account in cash_and_bank_accounts:
            # Beginning balance (as of start date)
            beginning_balance = account.get_balance(as_of_date=start_date_filter - timezone.timedelta(days=1))
            beginning_cash_balance += beginning_balance

            # Ending balance (as of end date)
            ending_balance = account.get_balance(as_of_date=end_date_filter)
            ending_cash_balance += ending_balance

        net_change_in_cash = ending_cash_balance - beginning_cash_balance

        # Update report data
        report_data['beginning_cash_balance'] = beginning_cash_balance
        report_data['ending_cash_balance'] = ending_cash_balance
        report_data['net_change_in_cash'] = net_change_in_cash
        
        # Calculate cash flow activities (simplified implementation)
        operating_activities = []
        investing_activities = []
        financing_activities = []

        # For now, we'll create a simplified cash flow statement
        # In a full implementation, you would analyze journal entries to categorize cash flows

        # Operating Activities (simplified - could be expanded to analyze revenue/expense accounts)
        if net_change_in_cash != Decimal('0.00'):
            operating_activities.append({
                'description': 'Net cash flow from operations (simplified)',
                'amount': net_change_in_cash
            })

        # Calculate totals
        net_cash_from_operating = sum(item['amount'] for item in operating_activities)
        net_cash_from_investing = sum(item['amount'] for item in investing_activities)
        net_cash_from_financing = sum(item['amount'] for item in financing_activities)

        # Update report data
        report_data.update({
            'operating_activities_items': operating_activities,
            'net_cash_from_operating': net_cash_from_operating,
            'investing_activities_items': investing_activities,
            'net_cash_from_investing': net_cash_from_investing,
            'financing_activities_items': financing_activities,
            'net_cash_from_financing': net_cash_from_financing,
            'report_generated_successfully': True
        })

        logger.info(f"Cash Flow Statement generated for period {start_date_filter} to {end_date_filter}")
        return report_data

    # The get_context_data method is now inherited from BaseReportViewMixin.
    # BaseReportViewMixin will call self.get_report_title(), self.get_filter_form_initial_data(),
    # initialize self.filter_form_class, and then call self.get_report_data(processed_form)
    # and put all returned keys from get_report_data into the context.

    # --- Your Export Methods (export_to_csv, export_to_excel, export_to_pdf) ---
    # Ensure these are correctly defined as methods of this class
    # and that they use `self.report_title` (the property) instead of `self.get_report_title()` if called directly.
    # BaseReportViewMixin's `get` method calls them with `report_data_dict`.
    
    # Example for one, ensure others are similar:
    def get_pdf_template_name(self): # Called by BaseReportViewMixin's export_to_pdf
        return 'reporting/pdf/cash_flow_statement_pdf.html'
    

    def export_to_excel(self, report_data_dict, request):
        # ... (Implement Excel for Cash Flow) ...
        messages.info(request, "Excel export for Cash Flow Statement is under development.")
        return redirect(request.path)

    def export_to_pdf(self, report_data_dict, request, html_context):
        pdf_context = {
            'report_data': report_data_dict, 'school_profile': self.get_school_profile(),
            'report_title': self.get_report_title(), 'current_datetime': timezone.now(),
        }
        pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            filename = f"{self.report_code}_{report_data_dict['start_date'].strftime('%Y%m%d')}_{report_data_dict['end_date'].strftime('%Y%m%d')}.pdf"
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            return response
        else:
            logger.error(f"PDF generation failed for {self.report_code} (Cash Flow)")
            messages.error(request, "Failed to generate PDF report.")
            return redirect(request.path)


# ==============================================================================
# Budget Variance Report
# ==============================================================================

# apps/reporting/views.py
import logging
from decimal import Decimal

from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib import messages
from django.db.models import Sum
from django.db.models.functions import Coalesce

# Models (ensure all are correctly imported)
from apps.schools.models import AcademicYear, Term # Assuming these are in schools app
from apps.finance.models import BudgetAmount, BudgetItem # Assuming these are in finance app
from apps.accounting.models import AccountType, JournalEntryItem # Corrected import for JELine if it's JournalEntryItem
# Note: If your JournalEntryLine is actually JournalEntryItem, use that name.

# Forms (your filter form for this report)
from .forms import BudgetVarianceReportForm # Or from wherever it's defined

logger = logging.getLogger(__name__)


from django.conf import settings
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.utils import timezone
from django.db.models import Sum, F, Q, DecimalField
from django.db.models.functions import Coalesce
from decimal import Decimal
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponse # For exports
from django.shortcuts import redirect

# Your model imports - CRITICAL: Ensure these paths and names are correct
from apps.accounting.models import Account, JournalEntry, JournalLine, AccountType
from apps.finance.models import BudgetAmount, BudgetItem # Assuming BudgetItem and BudgetAmount are here
from apps.schools.models import AcademicYear, Term, SchoolProfile # Assuming Term is here

# Your form import
from .forms import BudgetVarianceReportForm # Ensure this form is defined correctly

# Your mixin imports
from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin

# Your PDF utility and export libraries
from apps.common.utils import render_to_pdf
import csv
import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, Alignment
from io import BytesIO

import logging
logger = logging.getLogger(__name__)


class BudgetVarianceReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    permission_required = 'reporting.view_budget_variance_report'
    template_name = 'reporting/budget_variance_report.html'

    report_code = 'budget_variance_report'
    filter_form_class = BudgetVarianceReportForm

    report_code = 'RPT_BUDGET_VARIANCE'
    filter_form_class = BudgetReportFilterForm # Example, replace with your actual form
    report_title_text = _("Budget Performance & Variance")
    
    @property
    def report_title(self):
        form = getattr(self, 'processed_filter_form', None)
        ay_obj, term_obj = None, None
        if form:
            if form.is_bound and form.is_valid():
                ay_obj = form.cleaned_data.get('academic_year')
                term_obj = form.cleaned_data.get('term')
            elif not form.is_bound and form.initial:
                ay_pk = form.initial.get('academic_year')
                term_pk = form.initial.get('term')
                if ay_pk:
                    try: ay_obj = AcademicYear.objects.get(pk=ay_pk)
                    except AcademicYear.DoesNotExist: ay_obj = None
                if term_pk:
                    try: term_obj = Term.objects.get(pk=term_pk)
                    except Term.DoesNotExist: term_obj = None
        
        title = _("Budget vs Actuals (Variance Report)")
        if ay_obj:
            title += f" - AY: {ay_obj.name}"
            if term_obj:
                title += f", Term: {term_obj.name}"
        return title

    def get_filter_form_initial_data(self):
        current_ay = AcademicYear.objects.filter(is_active=True, is_current=True).first()
        if not current_ay:
            current_ay = AcademicYear.objects.filter(is_active=True).order_by('-start_date').first()
        return {'academic_year': current_ay.pk if current_ay else None, 'term': None}

    def get_report_data(self, filter_form=None): # Signature corrected
        """
        Fetches and calculates data for the Budget Variance Report.
        Uses self.form (set by BaseReportViewMixin.get()) for filter criteria.
        """
        academic_year_obj, term_obj = None, None
        
        # Initialize the report_data dictionary with all expected keys
        report_data = {
            'academic_year_name': None, 'term_name': None, 
            'budget_items_variance': [],
            'total_budgeted_income': Decimal('0.00'), 
            'total_actual_income': Decimal('0.00'),
            'total_variance_income': Decimal('0.00'),
            'total_budgeted_expense': Decimal('0.00'), 
            'total_actual_expense': Decimal('0.00'),
            'total_variance_expense': Decimal('0.00'),
            'net_budgeted_profit_loss': Decimal('0.00'),
            'net_actual_profit_loss': Decimal('0.00'),
            'net_variance_profit_loss': Decimal('0.00'),
            'report_generated_successfully': False, # Changed from 'report_generated'
            'filter_form_errors': None, # Store form errors if any
            'start_date_for_actuals': None, # For clarity in template
            'end_date_for_actuals': None,   # For clarity in template
        }

        filter_form = self.form # Access the form instance set by BaseReportViewMixin's get() method

        if filter_form: # Check if a filter form is even being used
            if filter_form.is_valid(): # Form was bound with GET data and is valid
                academic_year_obj = filter_form.cleaned_data.get('academic_year')
                term_obj = filter_form.cleaned_data.get('term') # Optional term
                logger.debug(f"{self.__class__.__name__}: Using valid filter data: AY={academic_year_obj}, Term={term_obj}")
            elif not filter_form.is_bound and filter_form.initial: # Form is unbound, use its initial data
                ay_pk = filter_form.initial.get('academic_year')
                term_pk = filter_form.initial.get('term')
                if ay_pk:
                    try: academic_year_obj = AcademicYear.objects.get(pk=ay_pk)
                    except AcademicYear.DoesNotExist: logger.warning(f"Initial AY PK {ay_pk} not found.")
                if term_pk:
                    try: term_obj = Term.objects.get(pk=term_pk)
                    except Term.DoesNotExist: logger.warning(f"Initial Term PK {term_pk} not found.")
                logger.debug(f"{self.__class__.__name__}: Using initial filter data: AY={academic_year_obj}, Term={term_obj}")
            else: # Form is bound but invalid
                report_data['filter_form_errors'] = filter_form.errors
                logger.warning(f"{self.__class__.__name__}.get_report_data: Filter form is bound but invalid. Errors: {filter_form.errors}")
                messages.error(self.request, _("Please correct the filter errors to generate the report."))
                return report_data # Return with report_generated_successfully = False
        else: # No filter form was defined/used
            logger.warning(f"{self.__class__.__name__}: No filter form provided. Cannot generate budget variance report without Academic Year.")
            messages.error(self.request, _("Report filters are missing. Cannot generate report."))
            return report_data
        
        if not academic_year_obj:
            logger.error(f"{self.__class__.__name__}: Academic Year is required but not found/selected.")
            messages.error(self.request, _("An Academic Year must be selected to generate the budget variance report."))
            return report_data # Return with report_generated_successfully = False

        report_data.update({
            'academic_year_name': academic_year_obj.name,
            'term_name': term_obj.name if term_obj else _("Full Academic Year"),
        })
        logger.info(f"Generating Budget Variance for AY: {academic_year_obj.name}{', Term: ' + term_obj.name if term_obj else ' (Full Year)'}")

        # Determine date range for actuals
        if term_obj and hasattr(term_obj, 'start_date') and hasattr(term_obj, 'end_date') and term_obj.start_date and term_obj.end_date:
            start_date_actuals = term_obj.start_date
            end_date_actuals = term_obj.end_date
        elif hasattr(academic_year_obj, 'start_date') and hasattr(academic_year_obj, 'end_date') and academic_year_obj.start_date and academic_year_obj.end_date:
            start_date_actuals = academic_year_obj.start_date
            end_date_actuals = academic_year_obj.end_date
        else:
            logger.error(f"Cannot determine valid date range for actuals for AY: {academic_year_obj.name}, Term: {term_obj.name if term_obj else 'N/A'}")
            messages.error(self.request, _("Could not determine the date range for calculating actual amounts."))
            return report_data
            
        report_data['start_date_for_actuals'] = start_date_actuals
        report_data['end_date_for_actuals'] = end_date_actuals

        report_lines_data = []
        
        budget_amounts_qs = BudgetAmount.objects.filter(academic_year=academic_year_obj)
        if term_obj:
            budget_amounts_qs = budget_amounts_qs.filter(term=term_obj)
        
        budget_amounts_qs = budget_amounts_qs.select_related(
            'budget_item', 
            'budget_item__linked_coa_account',
            'budget_item__linked_coa_account__account_type' # Account -> AccountType
        ).order_by('budget_item__budget_item_type', 'budget_item__name')

        for ba in budget_amounts_qs:
            budget_item = ba.budget_item
            budgeted_amount = ba.amount
            actual_amount = Decimal('0.00')

            if budget_item.linked_coa_account:
                account_to_check = budget_item.linked_coa_account
                
                # Use the Account model's get_balance method for period calculation
                actual_amount = account_to_check.get_balance(
                    start_date=start_date_actuals,
                    as_of_date=end_date_actuals
                )

            else:
                # No linked account, actual amount remains 0.00
                logger.info(f"Budget item '{budget_item.name}' has no linked CoA account. Actual amount will be 0.00.")
            
            variance = budgeted_amount - actual_amount
            variance_percentage = (variance / budgeted_amount * 100) if budgeted_amount != Decimal('0.00') else Decimal('0.00')
            
            is_favorable = False
            # Ensure BudgetItem.ItemTypeChoices.EXPENSE/REVENUE match your model enum
            if hasattr(BudgetItem, 'ItemTypeChoices'): # Check if enum exists
                if budget_item.budget_item_type == BudgetItem.ItemTypeChoices.EXPENSE:
                    if actual_amount <= budgeted_amount: is_favorable = True 
                elif budget_item.budget_item_type == BudgetItem.ItemTypeChoices.REVENUE:
                    if actual_amount >= budgeted_amount: is_favorable = True
            else:
                logger.warning("BudgetItem.ItemTypeChoices not found on BudgetItem model. Favorable status cannot be determined.")
            
            report_lines_data.append({
                'item_name': budget_item.name, 
                'account_code': budget_item.linked_coa_account.account_code if budget_item.linked_coa_account else 'N/A',
                'budget_item_type': budget_item.get_budget_item_type_display() if hasattr(budget_item, 'get_budget_item_type_display') else budget_item.budget_item_type,
                'budgeted': budgeted_amount, 
                'actual': actual_amount, 
                'variance': variance,
                'variance_percentage': variance_percentage, 
                'is_favorable': is_favorable
            })

            if hasattr(BudgetItem, 'ItemTypeChoices'):
                if budget_item.budget_item_type == BudgetItem.ItemTypeChoices.REVENUE:
                    report_data['total_budgeted_income'] += budgeted_amount
                    report_data['total_actual_income'] += actual_amount
                elif budget_item.budget_item_type == BudgetItem.ItemTypeChoices.EXPENSE:
                    report_data['total_budgeted_expense'] += budgeted_amount
                    report_data['total_actual_expense'] += actual_amount
        
        report_data['budget_items_variance'] = report_lines_data
        
        report_data['total_variance_income'] = report_data['total_budgeted_income'] - report_data['total_actual_income']
        report_data['total_variance_expense'] = report_data['total_budgeted_expense'] - report_data['total_actual_expense']
        
        report_data['net_budgeted_profit_loss'] = report_data['total_budgeted_income'] - report_data['total_budgeted_expense']
        report_data['net_actual_profit_loss'] = report_data['total_actual_income'] - report_data['total_actual_expense']
        report_data['net_variance_profit_loss'] = report_data['net_budgeted_profit_loss'] - report_data['net_actual_profit_loss']
        
        report_data['report_generated_successfully'] = True
        return report_data
    

    def export_to_excel(self, report_data_dict, request): # report_data is dict
        # ... (Implement Excel for Budget Variance) ...
        messages.info(request, "Excel export for Budget Variance Report is under development.")
        return redirect(request.path)

    def export_to_pdf(self, report_data_dict, request, html_context): # report_data is dict
        pdf_context = {
            'report_data': report_data_dict, 'school_profile': self.get_school_profile(),
            'report_title': self.get_report_title(), 'current_datetime': timezone.now(),
        }
        pdf = render_to_pdf(self.get_pdf_template_name(), pdf_context)
        if pdf:
            response = HttpResponse(pdf, content_type='application/pdf')
            ay_name = report_data_dict['academic_year'].name.replace(" ", "_") if report_data_dict.get('academic_year') else "period"
            term_name = f"_{report_data_dict['term'].name.replace(' ', '_')}" if report_data_dict.get('term') else ""
            filename = f"{self.report_code}_{ay_name}{term_name}.pdf"
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            return response
        else:
            logger.error(f"PDF generation failed for {self.report_code} (Budget Variance)")
            messages.error(request, "Failed to generate PDF report.")
            return redirect(request.path)


from django.views import View
from django.shortcuts import render


class ReportingHomeView(TenantLoginRequiredMixin, TemplateView):
    template_name = 'reporting/reporting_home.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Generate report links based on user permissions
        report_links = []
        user = self.request.user

        # Basic Reports
        if user.has_perm('reporting.view_outstanding_fees_report'):
            report_links.append({
                'name': 'Outstanding Fees Report',
                'url_name': 'reporting:outstanding_fees_report'
            })

        if user.has_perm('reporting.view_collection_report'):
            report_links.append({
                'name': 'Collection Report',
                'url_name': 'reporting:collection_report'
            })

        if user.has_perm('reporting.view_payment_summary_report'):
            report_links.append({
                'name': 'Payment Summary Report',
                'url_name': 'reporting:payment_summary_report'
            })

        if user.has_perm('reporting.view_student_ledger_report'):
            report_links.append({
                'name': 'Student Ledger Report',
                'url_name': 'reporting:student_ledger_report'
            })

        if user.has_perm('reporting.view_fee_projection_report'):
            report_links.append({
                'name': 'Fee Projection Report',
                'url_name': 'reporting:fee_projection_report'
            })

        # Advanced Financial Reports
        if user.has_perm('reporting.view_trial_balance_report'):
            report_links.append({
                'name': 'Trial Balance Report',
                'url_name': 'reporting:trial_balance_report'
            })

        if user.has_perm('reporting.view_income_statement_report') or user.has_perm('reporting.view_income_expense_report'):
            report_links.append({
                'name': 'Income Statement Report',
                'url_name': 'reporting:income_expense_report'
            })

        if user.has_perm('reporting.view_balance_sheet_report'):
            report_links.append({
                'name': 'Balance Sheet Report',
                'url_name': 'reporting:balance_sheet_report'
            })

        if user.has_perm('reporting.view_cash_flow_statement_report') or user.has_perm('reporting.view_cash_flow_statement'):
            report_links.append({
                'name': 'Cash Flow Statement Report',
                'url_name': 'reporting:cash_flow_statement_report'
            })

        if user.has_perm('reporting.view_budget_variance_report'):
            report_links.append({
                'name': 'Budget Variance Report',
                'url_name': 'reporting:budget_variance_report'
            })

        if user.has_perm('reporting.view_expense_report'):
            report_links.append({
                'name': 'Expense Report',
                'url_name': 'reporting:expense_report'
            })

        context['report_links'] = report_links
        context['view_title'] = 'Reports Dashboard'

        return context



# D:\school_fees_saas_v2\apps\reporting\views.py
from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.db.models import Sum, Q, F
from django.utils import timezone
import datetime

from apps.payments.models import Payment
from apps.fees.models import Invoice, StudentConcession
from apps.students.models import Student
from apps.common.utils import get_current_academic_year # Assuming you have this utility

from .forms import PaymentSummaryFilterForm, StudentLedgerFilterForm

# --- Base Report View (Optional, for common functionality) ---
class BaseReportView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    raise_exception = True # Or False for redirect to login

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['current_academic_year'] = get_current_academic_year(self.request)
        # Add other common report context if any
        return context



# --- Payment Summary Report View ---

# D:\school_fees_saas_v2\apps\reporting\views.py
from django.views.generic import ListView
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models import Sum, F, Q, ExpressionWrapper, DecimalField, Value, OuterRef, Subquery
from django.db.models.functions import Coalesce, TruncDate
from decimal import Decimal
import datetime # For string to date conversion if needed from GET params

from apps.common.mixins import TenantLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin
from apps.payments.models import Payment, PaymentMethod # Your Payment model
from apps.students.models import Student, SchoolClass, Section # For filtering potentially
from .forms import PaymentSummaryFilterForm # Your filter form for this report

import logging
logger = logging.getLogger(__name__)

class PaymentSummaryReportView(TenantLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin, ListView):
    model = Payment # The primary listing is of payments
    template_name = 'reporting/payment_summary_report.html' # Ensure this template exists
    context_object_name = 'payments_list' # For the paginated list of payments
    paginate_by = 30

    permission_required = 'reporting.view_payment_summary_report'
    report_title = _("Payment Summary Report")
    report_code = 'RPT_PAYMENT_SUMMARY'
    filter_form_class = PaymentSummaryFilterForm # Assign your Django Form for filtering

    def get_filter_form_initial_data(self):
        """Provides initial data for the filter form."""
        # Default to show all payments (no date filtering)
        return {
            'start_date': None,  # No start date filter
            'end_date': None     # No end date filter
        }

    def get_queryset(self):
        logger.debug(f"[{self.__class__.__name__}] get_queryset started. Request GET: {self.request.GET}")
        
        # Base queryset
        # Corrected select_related based on actual Payment model fields.
        queryset = Payment.objects.select_related(
            'student',
            'student__current_class',   # Student has current_class field
            'payment_method',
            'processed_by_staff',       # Links to StaffUser
            'created_by',               # Links to AUTH_USER_MODEL
            'parent_payer'              # Links to ParentUser
        ).prefetch_related(
            'allocations__invoice'      # Prefetch allocations to show which invoices a payment was allocated to
        ).order_by('-payment_date', '-created_at')

        # --- Filter Application ---
        # BaseReportViewMixin should handle instantiating self.form (from filter_form_class)
        # or self.filterset (from filterset_class) in its get_context_data or a setup method.
        # We then use the cleaned_data or filterset.qs here.

        self.form = None # Will be set by BaseReportViewMixin or in get_context_data if not ListView
        start_date = None
        end_date = None

        if hasattr(self, 'filter_form_class') and self.filter_form_class:
            # This assumes BaseReportViewMixin populates self.form with the instantiated form
            # and self.processed_filter_data with cleaned_data if valid.
            # We need to instantiate it here if BaseReportViewMixin doesn't do it before get_queryset.
            # For ListView, get_queryset is called before get_context_data.
            # So, let's instantiate the form here for filtering.
            
            initial_form_data = self.get_filter_form_initial_data() # From BaseReportViewMixin
            self.form = self.filter_form_class(self.request.GET or None, initial=initial_form_data, request=self.request)
            context_filter_form = self.form # Store for get_context_data later

            if self.form.is_valid():
                logger.debug(f"[{self.__class__.__name__}] Applying form filters: {self.form.cleaned_data}")
                cd = self.form.cleaned_data
                start_date = cd.get('start_date')
                end_date = cd.get('end_date')
                payment_method_filter = cd.get('payment_method')
                student_filter = cd.get('student')
                class_filter = cd.get('school_class') # Assuming field name in form

                if start_date:
                    queryset = queryset.filter(payment_date__gte=start_date)
                if end_date:
                    # Adjust end_date to include the whole day
                    end_date_adjusted = datetime.datetime.combine(end_date, datetime.time.max)
                    if settings.USE_TZ:
                        end_date_adjusted = timezone.make_aware(end_date_adjusted, timezone.get_default_timezone())
                    queryset = queryset.filter(payment_date__lte=end_date_adjusted)
                
                if payment_method_filter:
                    queryset = queryset.filter(payment_method=payment_method_filter)
                if student_filter:
                    queryset = queryset.filter(student=student_filter)
                if class_filter:
                    queryset = queryset.filter(student__current_class=class_filter)
                # ... other filters ...
            elif self.request.GET: # Form submitted but not valid
                logger.warning(f"[{self.__class__.__name__}] Filter form submitted but invalid: {self.form.errors}")
        else: # No filter form class defined
            # Show all payments without date filtering
            logger.debug(f"[{self.__class__.__name__}] No filter form class defined, showing all payments")
            pass  # No additional filtering - show all payments

        # Store start_date and end_date on self for get_report_data and get_context_data
        self.report_period_start = start_date
        self.report_period_end = end_date
            
        logger.debug(f"[{self.__class__.__name__}] get_queryset final count: {queryset.count()}")
        self.full_report_queryset = queryset # Store for aggregations and exports
        return queryset # This will be paginated by ListView

    def get_report_data(self, filter_form=None):
        logger.debug(f"[{self.__class__.__name__}] get_report_data called.")
        report_specific_data = {}

        # Ensure full_report_queryset is available by calling get_queryset if needed
        if not hasattr(self, 'full_report_queryset') or self.full_report_queryset is None:
            logger.debug(f"[{self.__class__.__name__}] full_report_queryset not set, calling get_queryset().")
            self.get_queryset()

        if hasattr(self, 'full_report_queryset') and self.full_report_queryset is not None:
            # Aggregate total amount from the filtered payments
            total_collected_agg = self.full_report_queryset.aggregate(
                total=Sum('amount') # <<< CORRECTED: Use 'amount' instead of 'amount_paid'
            )
            report_specific_data['total_amount_collected'] = total_collected_agg.get('total') or Decimal('0.00')

            # Example: Payments by payment method
            payments_by_method = self.full_report_queryset.values(
                'payment_method__name' # Group by payment method name
            ).annotate(
                total=Sum('amount'), # <<< CORRECTED
                count=Count('id')
            ).order_by('-total')
            report_specific_data['payments_by_method_summary'] = payments_by_method

            logger.debug(f"[{self.__class__.__name__}] Total collected: {report_specific_data['total_amount_collected']}")
        else:
            logger.warning(f"[{self.__class__.__name__}] full_report_queryset not available for aggregation in get_report_data.")
            report_specific_data['total_amount_collected'] = Decimal('0.00')
            report_specific_data['payments_by_method_summary'] = []
            
        return report_specific_data
    

    def get_context_data(self, **kwargs):
        # ListView's get_context_data calls get_queryset and sets up pagination.
        # BaseReportViewMixin's get_context_data then calls get_report_data.
        context = super().get_context_data(**kwargs) # This calls BaseReportViewMixin's get_context_data

        # Add report period to context for display in template
        context['report_period_start'] = getattr(self, 'report_period_start', None)
        context['report_period_end'] = getattr(self, 'report_period_end', None)
        
        # Ensure filter_form is in context if not already added by BaseReportViewMixin
        if 'filter_form' not in context and hasattr(self, 'form') and self.form:
            context['filter_form'] = self.form
        elif 'filter_form' not in context and hasattr(self, 'filterset') and self.filterset:
            context['filter_form'] = self.filterset.form


        # context['report_description'] from your original is good, can be set by BaseReportViewMixin too
        context['report_description'] = _("This report summarizes payments received within the selected period and filters.")
        
        logger.debug(f"[{self.__class__.__name__}] Final context keys: {list(context.keys())}")
        return context



# D:\school_fees_saas_v2\apps\reporting\views.py

import datetime
from decimal import Decimal
from django.utils import timezone
from django.shortcuts import get_object_or_404
from django.db.models import Sum, F, Q, Value, CharField
from django.db.models.functions import Coalesce, Concat

# Your project's models and mixins
from apps.common.mixins import TenantPermissionRequiredMixin, BaseReportViewMixin # Assuming your BaseReportViewMixin is here
from apps.students.models import Student
from apps.fees.models import Invoice, StudentConcession
from apps.payments.models import Payment
from .filters import StudentLedgerFilterForm # Assuming your filter form is here

class StudentLedgerReportView(TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    template_name = 'reporting/student_ledger_report.html'
    permission_required = 'reporting.view_student_ledger_report'
    
    # These attributes are for BaseReportViewMixin
    filter_form_class = StudentLedgerFilterForm
    report_code = 'RPT_STUDENT_LEDGER'
    
    def get_report_data(self, form):
        """
        Calculates and returns the full dataset for the student ledger.
        This method is called by the BaseReportViewMixin.
        """
        # Initialize the data dictionary that we will return
        data = {
            'student': None, 'transactions': [],
            'opening_balance': Decimal('0.00'), 'closing_balance': Decimal('0.00'),
            'total_debits': Decimal('0.00'), 'total_credits': Decimal('0.00'),
            'report_period_start': None, 'report_period_end': None,
        }

        # The form is already validated by the mixin. We just get the cleaned data.
        if form.is_valid() and form.cleaned_data.get('student'):
            student = form.cleaned_data['student']
            start_date = form.cleaned_data.get('start_date')
            end_date = form.cleaned_data.get('end_date') or timezone.now().date()

            if not start_date:
                first_invoice_date = Invoice.objects.filter(student=student).order_by('issue_date').values_list('issue_date', flat=True).first()
                start_date = first_invoice_date or (timezone.now().date() - timezone.timedelta(days=365))
            
            data['student'] = student
            data['report_period_start'] = start_date
            data['report_period_end'] = end_date
            
            # --- 1. Calculate Opening Balance ---
            ob_agg = Invoice.objects.filter(
                student=student, issue_date__lt=start_date
            ).aggregate(
                balance=Coalesce(Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')), Decimal('0.00'))
            )
            opening_balance = ob_agg['balance']
            data['opening_balance'] = opening_balance

            # --- 2. Fetch Transactions for the Period ---
            transactions_data = []

            # Invoices
            invoices_qs = Invoice.objects.filter(student=student, issue_date__range=(start_date, end_date))
            for inv in invoices_qs:
                net_debit = inv.subtotal_amount - inv.total_concession_amount
                transactions_data.append({
                    'date': inv.issue_date,
                    'description': f"Invoice #{inv.invoice_number_display}",
                    'debit': net_debit,
                    'credit': Decimal('0.00'),
                    'type': 'Invoice',
                    'reference_number': inv.invoice_number_display,
                    'reference_link': f"/portal/invoices/{inv.pk}/detail/"  # Adjust URL as needed
                })

            # Payments
            payments_qs = Payment.objects.filter(student=student, payment_date__date__range=(start_date, end_date), status=Payment.STATUS_COMPLETED)
            for pay in payments_qs:
                transactions_data.append({
                    'date': pay.payment_date.date(),
                    'description': f"Payment Received ({pay.payment_method.name if pay.payment_method else 'N/A'})",
                    'debit': Decimal('0.00'),
                    'credit': pay.amount,
                    'type': 'Payment',
                    'reference_number': pay.receipt_number_display,
                    'reference_link': f"/portal/payments/{pay.pk}/detail/"  # Adjust URL as needed
                })

            # Concessions - Temporarily disabled due to database migration issues
            # TODO: Re-enable once StudentConcession term_id field migration is resolved
            # concessions_qs = StudentConcession.objects.filter(student=student, granted_at__date__range=(start_date, end_date))
            # for conc in concessions_qs:
            #     transactions_data.append({'date': conc.granted_at.date(), 'description': f"Concession: {conc.concession_type.name if conc.concession_type else 'General'}", 'debit': Decimal('0.00'), 'credit': conc.amount})
            
            # --- 3. Sort transactions and calculate running balance ---
            sorted_transactions = sorted(transactions_data, key=lambda x: x['date'])
            
            running_balance = opening_balance
            total_debits_period = Decimal('0.00')
            total_credits_period = Decimal('0.00')
            
            for tx in sorted_transactions:
                debit = tx.get('debit', Decimal('0.00'))
                credit = tx.get('credit', Decimal('0.00'))
                
                total_debits_period += debit
                total_credits_period += credit
                
                running_balance = running_balance + debit - credit
                tx['balance'] = running_balance

            data['transactions'] = sorted_transactions
            data['total_debits'] = total_debits_period
            data['total_credits'] = total_credits_period
            data['closing_balance'] = running_balance
        
        return data
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs) # This will set filter_form from BaseReportViewMixin
        request = self.request
        form = context['filter_form']

        # Initialize context
        context.update({
            'student': None, 'transactions': [],
            'opening_balance': Decimal('0.00'), 'closing_balance': Decimal('0.00'),
            'total_debits': Decimal('0.00'), 'total_credits': Decimal('0.00'),
            'report_period_start': None, 'report_period_end': None,
        })

        if form.is_valid() and form.cleaned_data.get('student'):
            student = form.cleaned_data['student']
            start_date = form.cleaned_data.get('start_date')
            end_date = form.cleaned_data.get('end_date') or timezone.now().date()
            
            # If no start date, we can't calculate a meaningful opening balance,
            # so we'll show all history.
            if not start_date:
                # Find the earliest transaction to set a default start date
                first_invoice_date = Invoice.objects.filter(student=student).order_by('issue_date').values_list('issue_date', flat=True).first()
                if first_invoice_date:
                    start_date = first_invoice_date
                else:
                    start_date = timezone.now().date() # Fallback

            context.update({
                'student': student,
                'view_title': f"Student Ledger: {student.full_name}",
                'report_description': f"Financial transaction history for {student.full_name}.",
                'report_period_start': start_date,
                'report_period_end': end_date,
            })

            # --- 1. Calculate Opening Balance ---
            # Sum of (subtotal - concession - paid) for all invoices BEFORE the start date.
            opening_balance_agg = Invoice.objects.filter(
                student=student, issue_date__lt=start_date
            ).aggregate(
                balance=Coalesce(Sum(F('subtotal_amount') - F('total_concession_amount') - F('amount_paid')), Decimal('0.00'))
            )
            opening_balance = opening_balance_agg['balance']
            context['opening_balance'] = opening_balance

            # --- 2. Fetch Transactions for the Period ---
            transactions_data = []

            # Invoices
            invoices_qs = Invoice.objects.filter(student=student, issue_date__range=(start_date, end_date))
            for inv in invoices_qs:
                # The debit on a ledger is the net billable amount of the invoice
                net_debit = inv.subtotal_amount - inv.total_concession_amount
                transactions_data.append({
                    'date': inv.issue_date,
                    'description': f"Invoice #{inv.invoice_number_display}",
                    'debit': net_debit,
                    'credit': Decimal('0.00'),
                    'type': 'Invoice',
                    'reference_number': inv.invoice_number_display,
                    'reference_link': f"/portal/invoices/{inv.pk}/detail/"  # Adjust URL as needed
                })
                
            # Payments
            payments_qs = Payment.objects.filter(student=student, payment_date__date__range=(start_date, end_date), status=Payment.STATUS_COMPLETED)
            for pay in payments_qs:
                transactions_data.append({
                    'date': pay.payment_date.date(),
                    'description': f"Payment Received ({pay.payment_method.name if pay.payment_method else 'N/A'})",
                    'debit': Decimal('0.00'),
                    'credit': pay.amount,
                    'type': 'Payment',
                    'reference_number': pay.receipt_number_display,
                    'reference_link': f"/portal/payments/{pay.pk}/detail/"  # Adjust URL as needed
                })

            # Standalone Concessions (those not tied to an invoice's total)
            # Temporarily disabled due to database migration issues
            # TODO: Re-enable once StudentConcession term_id field migration is resolved
            # concessions_qs = StudentConcession.objects.filter(student=student, granted_at__date__range=(start_date, end_date))
            # for conc in concessions_qs:
            #     transactions_data.append({
            #         'date': conc.granted_at.date(),
            #         'description': f"Concession: {conc.concession_type.name if conc.concession_type else 'General'}",
            #         'debit': Decimal('0.00'),
            #         'credit': conc.amount,
            #     })
            
            # --- 3. Sort transactions and calculate running balance ---
            sorted_transactions = sorted(transactions_data, key=lambda x: x['date'])
            
            running_balance = opening_balance
            total_debits_period = Decimal('0.00')
            total_credits_period = Decimal('0.00')
            
            for tx in sorted_transactions:
                debit = tx.get('debit', Decimal('0.00'))
                credit = tx.get('credit', Decimal('0.00'))
                
                total_debits_period += debit
                total_credits_period += credit
                
                running_balance = running_balance + debit - credit
                tx['balance'] = running_balance

            context['transactions'] = sorted_transactions
            context['total_debits'] = total_debits_period
            context['total_credits'] = total_credits_period
            context['closing_balance'] = running_balance
        
        return context



class FeeProjectionReportView(TenantLoginRequiredMixin, TenantPermissionRequiredMixin, BaseReportViewMixin, TemplateView):
    template_name = 'reporting/fee_projection_report.html'
    permission_required = 'reporting.view_fee_projection_report'

    report_code = 'RPT_FEE_PROJECTION'
    report_title_text = _("Fee Projection Report")
    filter_form_class = None  # No filters for now

    def get_report_data(self, processed_filter_form=None):
        """Calculate fee projections based on current enrollment and fee structures."""
        try:
            from apps.students.models import Student
            from apps.schools.models import SchoolClass, AcademicYear
            from apps.fees.models import FeeStructure, FeeHead

            # Get current academic year
            current_year = AcademicYear.objects.filter(is_current=True).first()
            if not current_year:
                current_year = AcademicYear.objects.order_by('-start_date').first()

            projected_items = []
            total_projected = Decimal('0.00')

            # Get all active classes
            classes = SchoolClass.objects.filter(is_active=True).order_by('name')

            for class_obj in classes:
                # Count active students in this class
                student_count = Student.objects.filter(
                    current_class=class_obj,
                    is_active=True
                ).count()

                if student_count > 0:
                    # Get fee structures for this class
                    fee_structures = FeeStructure.objects.filter(
                        school_class=class_obj,
                        is_active=True,
                        academic_year=current_year
                    ).select_related('fee_head')

                    class_total = Decimal('0.00')
                    for fee_structure in fee_structures:
                        fee_amount = fee_structure.amount * student_count
                        class_total += fee_amount

                    if class_total > 0:
                        projected_items.append({
                            'name': f"{class_obj.name} ({student_count} students)",
                            'amount': class_total,
                            'notes': f"Based on current enrollment and fee structures"
                        })
                        total_projected += class_total

            return {
                'projected_items': projected_items,
                'total_projected': total_projected,
                'current_academic_year': current_year,
                'total_active_students': Student.objects.filter(is_active=True).count()
            }

        except Exception as e:
            logger.error(f"Error calculating fee projections: {e}")
            return {
                'projected_items': [],
                'total_projected': Decimal('0.00'),
                'error_message': 'Unable to calculate projections at this time.'
            }

