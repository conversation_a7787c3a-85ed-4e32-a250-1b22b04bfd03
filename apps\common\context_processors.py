# D:\school_fees_saas_v2\apps\common\context_processors.py

import logging
from django.conf import settings
from django.utils import timezone
from django.db.models import Q
from django_tenants.utils import get_public_schema_name, get_tenant_model
from django.contrib.auth import get_user_model

logger = logging.getLogger(__name__)

# --- Safe Model & Form Imports ---
def _safe_import(path):
    try:
        from django.apps import apps
        app_label, model_name = path.split('.')
        return apps.get_model(app_label, model_name)
    except Exception as e:
        logger.warning(f"Context Processor Safe Import Failed for '{path}': {e}")
        return None

School = get_tenant_model()
PublicUserModel = get_user_model()
StaffUser = _safe_import('schools.StaffUser')
ParentUser = _safe_import('students.ParentUser')
Subscription = _safe_import('subscriptions.Subscription')
AcademicYear = _safe_import('schools.AcademicYear')
PlatformAnnouncement = _safe_import('announcements.PlatformAnnouncement')
GlobalAcademicYearFilterForm = _safe_import('common.GlobalAcademicYearFilterForm')


# --- The SINGLE, CONSOLIDATED Context Processor ---
def global_context(request):
    """
    A single, consolidated context processor that provides all global context
    needed for both public and tenant-facing templates.
    """
    # 1. --- INITIALIZE CONTEXT AND GET CORE OBJECTS ---
    context = {}
    user = getattr(request, 'user', None)
    tenant = getattr(request, 'tenant', None)
    is_public_schema = not tenant or tenant.schema_name == get_public_schema_name()
    
    # This is the user whose permissions we will check.
    user_for_perms = getattr(request, 'effective_tenant_user', user)

    # 2. --- USER TYPE FLAGS ---
    user_type_flags = {'IS_AUTHENTICATED_USER': user and user.is_authenticated}
    if user_type_flags['IS_AUTHENTICATED_USER']:
        user_type_flags['IS_TENANT_STAFF_USER'] = StaffUser and isinstance(user_for_perms, StaffUser)
        user_type_flags['IS_TENANT_PARENT_USER'] = ParentUser and isinstance(user, ParentUser)
        user_type_flags['IS_PUBLIC_ADMIN_USER'] = PublicUserModel and isinstance(user, PublicUserModel) and user.is_staff
    context['user_type_flags'] = user_type_flags

    # 3. --- TENANT FEATURES & LIMITS ---
    # We now combine feature and limit fetching.
    active_features = {}
    plan_limits = {'MAX_STUDENTS': None, 'MAX_STAFF': None}
    
    if not is_public_schema and tenant and Subscription:
        try:
            subscription = tenant.subscription
            if subscription and subscription.is_usable and subscription.plan:
                plan = subscription.plan
                plan_limits['MAX_STUDENTS'] = plan.max_students
                plan_limits['MAX_STAFF'] = plan.max_staff
                active_feature_codes = set(plan.features.values_list('code', flat=True))
                active_features = {code: True for code in active_feature_codes}
        except (Subscription.DoesNotExist, AttributeError):
            logger.warning(f"CP: No valid subscription/plan found for tenant '{tenant.name}'.")
    
    context['tenant_features'] = active_features
    context['plan_limits'] = plan_limits
    
    # 4. --- NAVBAR FLAGS (Based on user type and permissions) ---
    navbar_flags = {} # Initialize empty

    # --- A) Handle Staff User Navbar ---
    if user_type_flags.get('IS_TENANT_STAFF_USER') and hasattr(user_for_perms, 'has_perm'):
        perms = user_for_perms
        
        # High-level Dropdown/Module Visibility
        navbar_flags['show_dashboard_link'] = perms.has_perm('schools.view_dashboard_summary')
        navbar_flags['show_manage_students_link'] = perms.has_perm('students.view_student') or perms.has_perm('students.view_parentuser')
        navbar_flags['show_hr_staff_dropdown'] = perms.has_perm('hr.view_hr_module')
        navbar_flags['show_finance_dropdown'] = perms.has_perm('finance.view_finance_module')
        navbar_flags['show_reports_dropdown'] = perms.has_perm('reporting.view_report_dashboard')
        navbar_flags['show_setup_admin_dropdown'] = perms.has_perm('portal_admin.view_setup_admin_module')
        navbar_flags['show_calendar_link'] = perms.has_perm('school_calendar.view_event')
        
        # High-level Dropdown/Module Visibility
        navbar_flags['show_dashboard_link'] = perms.has_perm('schools.view_dashboard_summary')
        navbar_flags['show_manage_students_link'] = perms.has_perm('students.view_student') or perms.has_perm('students.view_parentuser')
        navbar_flags['show_fees_management_dropdown'] = perms.has_perm('fees.view_invoice') or perms.has_perm('fees.view_feestructure')
        navbar_flags['show_finance_dropdown'] = perms.has_perm('finance.view_finance_module')
        navbar_flags['show_reports_dropdown'] = perms.has_perm('reporting.view_report_dashboard')
        navbar_flags['show_setup_admin_dropdown'] = perms.has_perm('portal_admin.view_setup_admin_module')
        navbar_flags['show_announcements_link'] = perms.has_perm('announcements.view_tenantannouncement')
        navbar_flags['show_calendar_link'] = perms.has_perm('school_calendar.view_event')

        # --- HR & STAFF MODULE (NEW GRANULAR LOGIC) ---
        
        # Granular permissions for each link INSIDE the dropdown
        navbar_flags['show_manage_staff_users_link'] = perms.has_perm('schools.view_staffuser')
        navbar_flags['show_leave_management_link'] = perms.has_perm('hr.approve_leave_requests')
        navbar_flags['show_leave_type_management_link'] = perms.has_perm('hr.manage_leave_types')
        navbar_flags['show_staff_leave_request_link'] = perms.has_perm('hr.add_leaverequest')
        navbar_flags['show_manage_leave_balances_link'] = perms.has_perm('hr.view_leavebalance')

        # --- ADD NEW FLAGS FOR PAYROLL SETUP ---
        navbar_flags['show_salary_grades_link'] = perms.has_perm('hr.view_salarygrade')
        navbar_flags['show_salary_components_link'] = perms.has_perm('hr.view_salarycomponent')
        navbar_flags['show_tax_brackets_link'] = perms.has_perm('hr.view_taxbracket')
        # ---

        # The main dropdown visibility now depends on all these flags
        navbar_flags['show_hr_staff_dropdown'] = any([
            navbar_flags.get('show_manage_staff_users_link'),
            navbar_flags.get('show_leave_management_link'),
            navbar_flags.get('show_staff_leave_request_link'),
            navbar_flags.get('show_salary_grades_link'),
            navbar_flags.get('show_salary_components_link'),
            navbar_flags.get('show_tax_brackets_link'),
        ])
        
        # --- END OF HR & STAFF MODULE LOGIC ---


        # --- CALENDAR MODULE ---
    
        # This is the high-level permission for the dropdown itself
        navbar_flags['show_calendar_dropdown'] = perms.has_perm('school_calendar.view_calendar_module')

        # These are the flags for the individual links inside the dropdown
        # Anyone who can see the module can view the calendar and the event list
        navbar_flags['show_view_calendar_link'] = perms.has_perm('school_calendar.view_event')
        navbar_flags['show_all_events_link'] = perms.has_perm('school_calendar.view_event')
        
        # Only users with the special 'manage_all_events' permission can create or manage events
        navbar_flags['show_create_event_link'] = perms.has_perm('school_calendar.manage_all_events')
        navbar_flags['show_manage_events_link'] = perms.has_perm('school_calendar.manage_all_events')
        
        # --- END OF CALENDAR MODULE LOGIC ---
    
    
        # --- FINANCE & ACCOUNTING MODULE ---
    
        # Granular permissions for each link
        navbar_flags['show_expenses_link'] = perms.has_perm('finance.view_expense')
        navbar_flags['show_budgets_link'] = perms.has_perm('finance.view_budget')
        navbar_flags['show_manage_vendors_link'] = perms.has_perm('finance.view_vendor')
        navbar_flags['show_manual_journal_entries_link'] = perms.has_perm('accounting.view_journalentry')
        navbar_flags['show_chart_of_accounts_link'] = perms.has_perm('accounting.view_account')
        
        # --- ADD THE NEW FLAGS HERE ---
        navbar_flags['show_general_ledger_link'] = perms.has_perm('reporting.view_general_ledger_report')
        navbar_flags['show_account_ledger_link'] = perms.has_perm('reporting.view_account_ledger_report')

        # High-level permission to show the entire "Finance" dropdown
        navbar_flags['show_finance_dropdown'] = (
            navbar_flags.get('show_expenses_link') or
            navbar_flags.get('show_budgets_link') or
            navbar_flags.get('show_manage_vendors_link') or
            navbar_flags.get('show_chart_of_accounts_link') or
            navbar_flags.get('show_manual_journal_entries_link') or
            navbar_flags.get('show_general_ledger_link') or
            navbar_flags.get('show_account_ledger_link')
        )
    
    
        # --- REPORTS MODULE ---
    
        # 1. Granular permissions for each report link
        navbar_flags['show_outstanding_fees_link'] = perms.has_perm('reporting.view_outstanding_fees_report')
        navbar_flags['show_collection_report_link'] = perms.has_perm('reporting.view_collection_report')
        navbar_flags['show_payment_summary_report_link'] = perms.has_perm('reporting.view_payment_summary_report')
        navbar_flags['show_student_ledger_report_link'] = perms.has_perm('reporting.view_student_ledger_report')
        navbar_flags['show_fee_projection_report_link'] = perms.has_perm('reporting.view_fee_projection_report')

        navbar_flags['show_expense_report_link'] = perms.has_perm('reporting.view_expense_report')
        navbar_flags['show_trial_balance_link'] = perms.has_perm('reporting.view_trial_balance_report')
        navbar_flags['show_income_statement_link'] = perms.has_perm('reporting.view_income_statement_report')
        navbar_flags['show_balance_sheet_link'] = perms.has_perm('reporting.view_balance_sheet_report')
        navbar_flags['show_cash_flow_link'] = perms.has_perm('reporting.view_cash_flow_statement_report')
        
        navbar_flags['show_budget_variance_report_link'] = perms.has_perm('reporting.view_budget_variance_report')

        navbar_flags['show_payroll_summary_report_link'] = perms.has_perm('hr.view_payroll_summary_report')

        # 2. High-level permission to show the entire "Reports" dropdown
        # This is TRUE if any of the granular link flags are TRUE.
        # We can also check the main module permission first.
        if perms.has_perm('reporting.view_report_dashboard'):
            navbar_flags['show_reports_dropdown'] = (
                navbar_flags.get('show_outstanding_fees_link') or
                navbar_flags.get('show_collection_report_link') or
                navbar_flags.get('show_payment_summary_report_link') or
                navbar_flags.get('show_student_ledger_report_link') or
                navbar_flags.get('show_fee_projection_report_link') or
                navbar_flags.get('show_expense_report_link') or
                navbar_flags.get('show_trial_balance_link') or
                navbar_flags.get('show_income_statement_link') or
                navbar_flags.get('show_balance_sheet_link') or
                navbar_flags.get('show_cash_flow_link') or
                navbar_flags.get('show_budget_variance_report_link') or
                navbar_flags.get('show_payroll_summary_report_link')
            )
        else:
            navbar_flags['show_reports_dropdown'] = False
            
            
        # --- SETUP & ADMIN MODULE ---

        # 1. High-level permission to show the entire dropdown
        navbar_flags['show_setup_admin_dropdown'] = perms.has_perm('portal_admin.view_setup_admin_module')

        # 2. Granular permissions for each link INSIDE the dropdown
        
        # School & Academic Setup
        navbar_flags['show_school_profile_link'] = perms.has_perm('schools.change_schoolprofile')
        navbar_flags['show_manage_classes_link'] = perms.has_perm('schools.view_schoolclass')
        navbar_flags['show_manage_academic_years_link'] = perms.has_perm('schools.view_academicyear')
        navbar_flags['show_manage_terms_link'] = perms.has_perm('schools.view_term')
        # Note: `show_announcements_link` is already set in the top-level section.

        # Financial Setup
        navbar_flags['show_manage_fee_heads_link'] = perms.has_perm('fees.view_feehead')
        navbar_flags['show_manage_fee_structures_link'] = perms.has_perm('fees.view_feestructure')
        navbar_flags['show_manage_payment_methods_link'] = perms.has_perm('payments.view_paymentmethod')
        
        # --- ADD THE NEW FLAG FOR VENDORS HERE ---
        # This uses Django's default 'view' permission for the Vendor model.
        navbar_flags['show_manage_vendors_link'] = perms.has_perm('finance.view_vendor')
        

        # Tenant Administration
        navbar_flags['show_manage_roles_link'] = perms.has_perm('auth.view_group')
        navbar_flags['show_assign_staff_to_roles_link'] = perms.has_perm('portal_admin.assign_staff_roles')
        navbar_flags['show_activity_log_link'] = perms.has_perm('portal_admin.view_adminactivitylog')
    
        
        # --- FEES MANAGEMENT MODULE ---
    
        # 1. Granular permissions for each link INSIDE the dropdown
        navbar_flags['show_invoices_link'] = perms.has_perm('fees.view_invoice')
        navbar_flags['show_concessions_link'] = perms.has_perm('fees.view_concessiontype')
        
        # 2. High-level permission to show the entire "Fees Mgt" dropdown
        # This is TRUE if the user can see EITHER invoices OR concessions.
        navbar_flags['show_fees_management_dropdown'] = (
            navbar_flags.get('show_invoices_link') or
            navbar_flags.get('show_concessions_link')
            )
            # --- END OF FEES MANAGEMENT MODULE LOGIC ---

    # --- B) Handle Parent User Navbar ---
    elif user_type_flags.get('IS_TENANT_PARENT_USER'):
        # Check if the Parent Portal feature is enabled for the tenant
        if active_features.get('PARENT_PORTAL', False):
            logger.debug(f"CP: Setting parent navbar flags for {user.email}. PARENT_PORTAL is ON.")
            navbar_flags['show_parent_dashboard_link'] = True
            navbar_flags['show_parent_children_fees_link'] = True
            navbar_flags['show_parent_payment_history_link'] = True
            navbar_flags['show_parent_profile_link'] = True
            
            # Conditionally show the online payment link based on its feature flag
            if active_features.get('ONLINE_PAYMENTS', False):
                navbar_flags['show_parent_online_payment_link'] = True
        else:
            logger.warning(f"CP: PARENT_PORTAL feature is OFF for tenant {tenant.name}. Hiding all parent navbar links.")

    context['navbar_flags'] = navbar_flags



    # 5. --- GLOBAL ACADEMIC YEAR FILTER ---
    selected_ay_obj = None
    ay_form = None
    if user_type_flags.get('IS_TENANT_STAFF_USER') and not is_public_schema and GlobalAcademicYearFilterForm and AcademicYear:
        session_key = f"selected_global_ay_pk_{tenant.schema_name}"
        selected_pk = request.session.get(session_key)
        ay_form = GlobalAcademicYearFilterForm(tenant=tenant, initial={'academic_year': selected_pk})
        if selected_pk:
            selected_ay_obj = AcademicYear.objects.filter(pk=selected_pk).first()
    context['global_academic_year_filter_form'] = ay_form
    context['selected_global_academic_year_obj'] = selected_ay_obj

    # 6. --- PLATFORM-WIDE ANNOUNCEMENTS ---
    if PlatformAnnouncement and not is_public_schema:
        now = timezone.now()
        context['latest_platform_announcement'] = PlatformAnnouncement.objects.filter(
            is_published=True, publish_date__lte=now
        ).filter(
            Q(expiry_date__isnull=True) | Q(expiry_date__gte=now)
        ).order_by('-publish_date').first()

    # 7. --- OTHER GLOBAL CONTEXT ---
    if School and user_type_flags.get('IS_PUBLIC_ADMIN_USER') and hasattr(user, 'owned_schools'):
        context['owned_school_for_public_admin'] = user.owned_schools.filter(is_active=True).first()

    context['current_tenant'] = tenant
    context['DJANGO_DEBUG'] = settings.DEBUG
    
    return context




# import logging
# from django.conf import settings
# from django.utils import timezone
# from django.db.models import Q
# from django_tenants.utils import get_public_schema_name, get_tenant_model
# from django.contrib.auth import get_user_model

# # --- Define a single logger for this module ---
# logger = logging.getLogger(__name__)

# # --- Safe Model & Form Imports ---
# def _safe_import(path):
#     try:
#         from django.apps import apps
#         app_label, model_name = path.split('.')
#         return apps.get_model(app_label, model_name)
#     except (ImportError, LookupError, ValueError) as e:
#         logger.warning(f"Context Processor Safe Import Failed for '{path}': {e}")
#         return None

# School = get_tenant_model()
# PublicUserModel = get_user_model()
# StaffUser = _safe_import('schools.StaffUser')
# ParentUser = _safe_import('students.ParentUser')
# Subscription = _safe_import('subscriptions.Subscription')
# AcademicYear = _safe_import('schools.AcademicYear')
# PlatformAnnouncement = _safe_import('announcements.PlatformAnnouncement')
# GlobalAcademicYearFilterForm = _safe_import('common.GlobalAcademicYearFilterForm')


# # --- The SINGLE, CONSOLIDATED Context Processor ---
# def global_context(request):
#     """
#     A single, consolidated context processor that provides all global context
#     needed for both public and tenant-facing templates.
#     """
#     # 1. --- INITIALIZE CONTEXT AND GET CORE OBJECTS ---
#     context = {}
#     user = getattr(request, 'user', None)
#     tenant = getattr(request, 'tenant', None)
#     is_public_schema = not tenant or tenant.schema_name == get_public_schema_name()
    
#     user_for_perms = getattr(request, 'effective_tenant_user', user)

#     # 2. --- USER TYPE FLAGS ---
#     user_type_flags = {'IS_AUTHENTICATED_USER': user and user.is_authenticated}
#     if user_type_flags['IS_AUTHENTICATED_USER']:
#         user_type_flags['IS_TENANT_STAFF_USER'] = StaffUser and isinstance(user_for_perms, StaffUser)
#         user_type_flags['IS_TENANT_PARENT_USER'] = ParentUser and isinstance(user, ParentUser)
#         is_public_staff = PublicUserModel and isinstance(user, PublicUserModel) and user.is_staff
#         user_type_flags['IS_PUBLIC_ADMIN_USER'] = is_public_staff
#     context['user_type_flags'] = user_type_flags

#     # 3. --- TENANT-SPECIFIC CONTEXT ---
#     plan_limits = {'MAX_STUDENTS': None, 'MAX_STAFF': None}
    
#     if not is_public_schema and tenant and Subscription:
#         try:
#             subscription = tenant.subscription
#             if subscription and subscription.is_usable and subscription.plan:
#                 plan = subscription.plan
#                 plan_limits['MAX_STUDENTS'] = plan.max_students
#                 plan_limits['MAX_STAFF'] = plan.max_staff
#         except (Subscription.DoesNotExist, AttributeError):
#             logger.warning(f"CP: No valid subscription/plan found for tenant '{tenant.name}'.")
#     context['plan_limits'] = plan_limits
    
#     # 4. --- NAVBAR FLAGS (Based purely on permissions) ---
#     navbar_flags = {}
#     if user_type_flags.get('IS_TENANT_STAFF_USER') and hasattr(user_for_perms, 'has_perm'):
#         perms = user_for_perms
        
#         # High-level Dropdown/Module Visibility
#         navbar_flags['show_dashboard_link'] = perms.has_perm('schools.view_dashboard_summary')
#         navbar_flags['show_manage_students_link'] = perms.has_perm('students.view_student') or perms.has_perm('students.view_parentuser')
#         navbar_flags['show_fees_management_dropdown'] = perms.has_perm('fees.view_invoice') or perms.has_perm('fees.view_feestructure')
#         navbar_flags['show_finance_dropdown'] = perms.has_perm('finance.view_finance_module')
#         navbar_flags['show_reports_dropdown'] = perms.has_perm('reporting.view_report_dashboard')
#         navbar_flags['show_setup_admin_dropdown'] = perms.has_perm('portal_admin.view_setup_admin_module')
#         navbar_flags['show_announcements_link'] = perms.has_perm('announcements.view_tenantannouncement')
#         navbar_flags['show_calendar_link'] = perms.has_perm('school_calendar.view_event')

#         # --- HR & STAFF MODULE (NEW GRANULAR LOGIC) ---
        
#         # Granular permissions for each link INSIDE the dropdown
#         navbar_flags['show_manage_staff_users_link'] = perms.has_perm('schools.view_staffuser')
#         navbar_flags['show_leave_management_link'] = perms.has_perm('hr.approve_leave_requests')
#         navbar_flags['show_leave_type_management_link'] = perms.has_perm('hr.manage_leave_types')
#         navbar_flags['show_staff_leave_request_link'] = perms.has_perm('hr.add_leaverequest')

#         # High-level permission to show the entire "HR & Staff" dropdown
#         # This is now TRUE if any of the granular link flags are TRUE.
#         navbar_flags['show_hr_staff_dropdown'] = (
#             navbar_flags['show_manage_staff_users_link'] or
#             navbar_flags['show_leave_management_link'] or
#             navbar_flags['show_leave_type_management_link'] or
#             navbar_flags['show_staff_leave_request_link']
#         )
#         # --- END OF HR & STAFF MODULE LOGIC ---


#         # --- CALENDAR MODULE ---
    
#         # This is the high-level permission for the dropdown itself
#         navbar_flags['show_calendar_dropdown'] = perms.has_perm('school_calendar.view_calendar_module')

#         # These are the flags for the individual links inside the dropdown
#         # Anyone who can see the module can view the calendar and the event list
#         navbar_flags['show_view_calendar_link'] = perms.has_perm('school_calendar.view_event')
#         navbar_flags['show_all_events_link'] = perms.has_perm('school_calendar.view_event')
        
#         # Only users with the special 'manage_all_events' permission can create or manage events
#         navbar_flags['show_create_event_link'] = perms.has_perm('school_calendar.manage_all_events')
#         navbar_flags['show_manage_events_link'] = perms.has_perm('school_calendar.manage_all_events')
        
#         # --- END OF CALENDAR MODULE LOGIC ---
    
    
#         # --- FINANCE & ACCOUNTING MODULE ---
    
#         # Granular permissions for each link
#         navbar_flags['show_expenses_link'] = perms.has_perm('finance.view_expense')
#         navbar_flags['show_budgets_link'] = perms.has_perm('finance.view_budget')
#         navbar_flags['show_manage_vendors_link'] = perms.has_perm('finance.view_vendor')
#         navbar_flags['show_manual_journal_entries_link'] = perms.has_perm('accounting.view_journalentry')
#         navbar_flags['show_chart_of_accounts_link'] = perms.has_perm('accounting.view_account')
        
#         # --- ADD THE NEW FLAGS HERE ---
#         navbar_flags['show_general_ledger_link'] = perms.has_perm('reporting.view_general_ledger_report')
#         navbar_flags['show_account_ledger_link'] = perms.has_perm('reporting.view_account_ledger_report')

#         # High-level permission to show the entire "Finance" dropdown
#         navbar_flags['show_finance_dropdown'] = (
#             navbar_flags.get('show_expenses_link') or
#             navbar_flags.get('show_budgets_link') or
#             navbar_flags.get('show_chart_of_accounts_link') or
#             navbar_flags.get('show_manual_journal_entries_link') or
#             navbar_flags.get('show_general_ledger_link') or
#             navbar_flags.get('show_account_ledger_link')
#         )
    
    
#         # --- REPORTS MODULE ---
    
#         # 1. Granular permissions for each report link
#         navbar_flags['show_outstanding_fees_link'] = perms.has_perm('reporting.view_outstanding_fees_report')
#         navbar_flags['show_collection_report_link'] = perms.has_perm('reporting.view_collection_report')
#         navbar_flags['show_payment_summary_report_link'] = perms.has_perm('reporting.view_payment_summary_report')
#         navbar_flags['show_student_ledger_report_link'] = perms.has_perm('reporting.view_student_ledger_report')
#         navbar_flags['show_fee_projection_report_link'] = perms.has_perm('reporting.view_fee_projection_report')

#         navbar_flags['show_expense_report_link'] = perms.has_perm('reporting.view_expense_report')
#         navbar_flags['show_trial_balance_link'] = perms.has_perm('reporting.view_trial_balance_report')
#         navbar_flags['show_income_statement_link'] = perms.has_perm('reporting.view_income_statement_report')
#         navbar_flags['show_balance_sheet_link'] = perms.has_perm('reporting.view_balance_sheet_report')
#         navbar_flags['show_cash_flow_link'] = perms.has_perm('reporting.view_cash_flow_statement_report')
        
#         navbar_flags['show_budget_variance_report_link'] = perms.has_perm('reporting.view_budget_variance_report')

#         # 2. High-level permission to show the entire "Reports" dropdown
#         # This is TRUE if any of the granular link flags are TRUE.
#         # We can also check the main module permission first.
#         if perms.has_perm('reporting.view_report_dashboard'):
#             navbar_flags['show_reports_dropdown'] = (
#                 navbar_flags.get('show_outstanding_fees_link') or
#                 navbar_flags.get('show_collection_report_link') or
#                 navbar_flags.get('show_payment_summary_report_link') or
#                 navbar_flags.get('show_student_ledger_report_link') or
#                 navbar_flags.get('show_fee_projection_report_link') or
#                 navbar_flags.get('show_expense_report_link') or
#                 navbar_flags.get('show_trial_balance_link') or
#                 navbar_flags.get('show_income_statement_link') or
#                 navbar_flags.get('show_balance_sheet_link') or
#                 navbar_flags.get('show_cash_flow_link') or
#                 navbar_flags.get('show_budget_variance_report_link')
#             )
#         else:
#             navbar_flags['show_reports_dropdown'] = False
            
            
#         # --- SETUP & ADMIN MODULE ---

#         # 1. High-level permission to show the entire dropdown
#         navbar_flags['show_setup_admin_dropdown'] = perms.has_perm('portal_admin.view_setup_admin_module')

#         # 2. Granular permissions for each link INSIDE the dropdown
        
#         # School & Academic Setup
#         navbar_flags['show_school_profile_link'] = perms.has_perm('schools.change_schoolprofile')
#         navbar_flags['show_manage_classes_link'] = perms.has_perm('schools.view_schoolclass')
#         navbar_flags['show_manage_academic_years_link'] = perms.has_perm('schools.view_academicyear')
#         navbar_flags['show_manage_terms_link'] = perms.has_perm('schools.view_term')
#         # Note: `show_announcements_link` is already set in the top-level section.

#         # Financial Setup
#         navbar_flags['show_manage_fee_heads_link'] = perms.has_perm('fees.view_feehead')
#         navbar_flags['show_manage_fee_structures_link'] = perms.has_perm('fees.view_feestructure')
#         navbar_flags['show_manage_payment_methods_link'] = perms.has_perm('payments.view_paymentmethod')
        
#         # --- ADD THE NEW FLAG FOR VENDORS HERE ---
#         # This uses Django's default 'view' permission for the Vendor model.
#         navbar_flags['show_manage_vendors_link'] = perms.has_perm('finance.view_vendor')
        

#         # Tenant Administration
#         navbar_flags['show_manage_roles_link'] = perms.has_perm('auth.view_group')
#         navbar_flags['show_assign_staff_to_roles_link'] = perms.has_perm('portal_admin.assign_staff_roles')
#         navbar_flags['show_activity_log_link'] = perms.has_perm('portal_admin.view_adminactivitylog')
    
        
#         # --- FEES MANAGEMENT MODULE ---
    
#         # 1. Granular permissions for each link INSIDE the dropdown
#         navbar_flags['show_invoices_link'] = perms.has_perm('fees.view_invoice')
#         navbar_flags['show_concessions_link'] = perms.has_perm('fees.view_concessiontype')
        
#         # 2. High-level permission to show the entire "Fees Mgt" dropdown
#         # This is TRUE if the user can see EITHER invoices OR concessions.
#         navbar_flags['show_fees_management_dropdown'] = (
#             navbar_flags.get('show_invoices_link') or
#             navbar_flags.get('show_concessions_link')
#         )
#         # --- END OF FEES MANAGEMENT MODULE LOGIC ---
        
#     elif user_type_flags.get('IS_TENANT_PARENT_USER'):
#         # If the user is a parent, we set their specific set of flags.
#         # We also check if the PARENT_PORTAL feature is enabled for the tenant.
        
#         # The 'tenant_features' dict comes from the logic in Step 3 of this processor
#         if context.get('tenant_features', {}).get('PARENT_PORTAL', False):
#             logger.debug(f"CP: Setting parent navbar flags for {user.email}. PARENT_PORTAL is ON.")
#             navbar_flags['show_parent_dashboard_link'] = True
#             navbar_flags['show_parent_children_fees_link'] = True
#             navbar_flags['show_parent_payment_history_link'] = True
#             navbar_flags['show_parent_profile_link'] = True
            
#             # Conditionally show the online payment link
#             if context.get('tenant_features', {}).get('ONLINE_PAYMENTS', False):
#                 navbar_flags['show_parent_online_payment_link'] = True
#         else:
#             logger.warning(f"CP: PARENT_PORTAL feature is OFF for tenant {tenant.name}. Hiding all parent navbar links.")
#             # All parent flags will remain False (their default state)
        
#     context['navbar_flags'] = navbar_flags
    
    
    


