{# apps/reporting/templates/reporting/payment_summary_report.html #}
{% extends "tenant_base.html" %}
{% load static humanize widget_tweaks %}

{% block tenant_page_title %}Payment Summary Report{% endblock %}

{% block extra_tenant_css %}
    {{ block.super }}
    <style>
        .report-header {
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
        }
        .report-header h1 { font-weight: 600; color: #343a40; }

        .summary-table th { background-color: #e9ecef; font-weight: 600; white-space: nowrap; }
        .summary-table td, .summary-table th { vertical-align: middle; }
        .total-row td { font-weight: bold; border-top: 2px solid #adb5bd; background-color: #f8f9fa; }
        .print-button-area { margin-bottom: 1rem; }
    </style>
{% endblock %}

{% block tenant_specific_content %}
<div class="container-fluid">
    <div class="report-header d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="bi bi-cash-stack me-2"></i>Payment Summary Report</h1>
            <p class="text-muted mb-0">Summary of payments received within the selected period.</p>
        </div>
        <div class="print-button-area">
            <button class="btn btn-outline-secondary btn-sm" onclick="window.print();">
                <i class="bi bi-printer me-1"></i> Print Summary
            </button>
            {# Add Export to PDF/CSV buttons here later #}
        </div>
    </div>


    {% include "partials/_messages.html" %}
    {% include "partials/_report_filter_form.html" with filter_form=filter_form report_code="payment_summary" specific_filter_title="Payment Summary Filters" %}

    <div class="card shadow-sm">
        <div class="card-header bg-light py-3">
            <h5 class="mb-0">Payments Received: {{ report_period_start|date:"j M Y" }} - {{ report_period_end|date:"j M Y" }}</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover summary-table mb-0">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" style="width: 10%;">Date Paid</th>
                            <th scope="col">Student Name</th>
                            <th scope="col" style="width: 15%;">Class</th>
                            <th scope="col" style="width: 12%;">Invoice #</th>
                            <th scope="col" style="width: 12%;">Payment Ref #</th>
                            <th scope="col" style="width: 15%;">Payment Method</th>
                            <th scope="col" class="text-end" style="width: 12%;">Amount Paid ({{ school_profile.currency_symbol|default:'$' }})</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments_list %}
                        <tr>
                            <td>{{ payment.payment_date|date:"Y-m-d" }}</td>
                            <td>
                                {% if payment.student %}
                                    <a href="{% url 'students:student_detail' pk=payment.student.pk %}">{{ payment.student.get_full_name }}</a>
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>{{ payment.student.current_class_and_section|default:"N/A" }}</td>
                            <td>
                                {% comment %}Payment doesn't have direct invoice FK, show allocations{% endcomment %}
                                {% if payment.allocations.exists %}
                                    {% for allocation in payment.allocations.all %}
                                        <a href="{% url 'fees:invoice_detail' pk=allocation.invoice.pk %}">{{ allocation.invoice.invoice_number }}</a>{% if not forloop.last %}, {% endif %}
                                    {% endfor %}
                                {% else %}
                                    Unallocated
                                {% endif %}
                            </td>
                            <td>{{ payment.transaction_reference|default:"N/A" }}</td>
                            <td>{{ payment.payment_method.name|default:"N/A" }}</td>
                            <td class="text-end">{{ payment.amount|floatformat:2|intcomma }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center text-muted py-4">No payments found for the selected criteria.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    {% if payments_list %}
                    <tfoot>
                        <tr class="total-row table-light">
                            <td colspan="6" class="text-end"><strong>Total Amount Collected:</strong></td>
                            <td class="text-end"><strong>{{ total_amount_collected|floatformat:2|intcomma }}</strong></td>
                        </tr>
                    </tfoot>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock tenant_specific_content %}