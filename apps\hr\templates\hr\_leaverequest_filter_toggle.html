{# apps/hr/templates/hr/_leaverequest_filter_toggle.html - Leave Request List Toggle Filter #}
{% load i18n widget_tweaks %}

{% if filter_form %}
<style>
    /* Premium Leave Request Filter Toggle Design */
    .premium-leaverequest-filter-card {
        border: none;
        border-radius: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .premium-leaverequest-filter-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .premium-leaverequest-filter-header {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .premium-leaverequest-filter-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .premium-leaverequest-filter-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        z-index: 1;
    }

    .premium-leaverequest-filter-toggle {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.75rem;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .premium-leaverequest-filter-toggle:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .premium-leaverequest-filter-body {
        padding: 2rem;
        background: white;
    }

    .form-floating-leaverequest {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .form-floating-leaverequest .form-control,
    .form-floating-leaverequest .form-select {
        height: 3.5rem;
        padding: 1rem 3rem 1rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: #ffffff;
    }

    .form-floating-leaverequest .form-control:focus,
    .form-floating-leaverequest .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.15);
        outline: none;
    }

    .form-floating-leaverequest label {
        position: absolute;
        top: 50%;
        left: 1rem;
        transform: translateY(-50%);
        background-color: white;
        padding: 0 0.5rem;
        color: #6c757d;
        font-size: 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        pointer-events: none;
        z-index: 2;
    }

    .form-floating-leaverequest .form-control:focus + label,
    .form-floating-leaverequest .form-control:not(:placeholder-shown) + label,
    .form-floating-leaverequest .form-select:focus + label,
    .form-floating-leaverequest .form-select:not([value=""]) + label {
        top: 0;
        font-size: 0.875rem;
        color: #28a745;
        font-weight: 600;
    }

    .premium-leaverequest-field-icon {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 1.1rem;
        z-index: 3;
        pointer-events: none;
    }

    .btn-leaverequest-premium {
        padding: 0.75rem 2rem;
        border-radius: 1rem;
        font-weight: 600;
        font-size: 1rem;
        border: none;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        min-width: 140px;
    }

    .btn-leaverequest-premium-primary {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-leaverequest-premium-primary:hover {
        background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .btn-leaverequest-premium-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .btn-leaverequest-premium-secondary:hover {
        background: linear-gradient(135deg, #495057 0%, #343a40 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        color: white;
    }

    .btn-export-leaverequest {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        border: none;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-left: 0.5rem;
    }

    .btn-export-leaverequest:hover {
        background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
        color: white;
    }

    .btn-import-leaverequest {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
        border: none;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-left: 0.5rem;
    }

    .btn-import-leaverequest:hover {
        background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .leaverequest-actions-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-top: 1.5rem;
        border: 1px solid #dee2e6;
    }

    .export-import-buttons-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;
        z-index: 1;
    }

    @media (max-width: 768px) {
        .premium-leaverequest-filter-header {
            padding: 1rem;
        }

        .premium-leaverequest-filter-title {
            font-size: 1.1rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .premium-leaverequest-filter-body {
            padding: 1.5rem;
        }

        .btn-leaverequest-premium {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
            min-width: 120px;
        }

        .export-import-buttons-container {
            flex-direction: column;
            align-items: stretch;
        }

        .btn-export-leaverequest,
        .btn-import-leaverequest {
            margin-left: 0;
            margin-top: 0.5rem;
        }
    }
</style>

<div class="premium-leaverequest-filter-card filter-card" id="leaveRequestFilterCard">
    <div class="premium-leaverequest-filter-header">
        <div class="premium-leaverequest-filter-title">
            <span>
                <i class="bi bi-calendar-check-fill me-2"></i>{% trans "Leave Request Filters & Data Management" %}
            </span>
            <div class="d-flex align-items-center">
                <div class="export-import-buttons-container">
                    <a href="?export=csv" class="btn btn-export-leaverequest" title="{% trans 'Export to CSV' %}">
                        <i class="bi bi-file-earmark-spreadsheet me-1"></i>CSV
                    </a>
                    <a href="?export=excel" class="btn btn-export-leaverequest" title="{% trans 'Export to Excel' %}">
                        <i class="bi bi-file-earmark-excel me-1"></i>Excel
                    </a>
                    <a href="?export=pdf" class="btn btn-export-leaverequest" title="{% trans 'Export to PDF' %}" target="_blank">
                        <i class="bi bi-file-earmark-pdf me-1"></i>PDF
                    </a>
                </div>
                <a class="premium-leaverequest-filter-toggle ms-3" data-bs-toggle="collapse" href="#leaveRequestFilterCollapse" role="button" aria-expanded="{% if request.GET and not request.GET.export %}true{% else %}false{% endif %}" aria-controls="leaveRequestFilterCollapse">
                    <i class="bi bi-chevron-down me-1"></i>{% trans "Toggle Filters" %}
                </a>
            </div>
        </div>
    </div>
    <div class="collapse {% if request.GET and not request.GET.export %}show{% endif %}" id="leaveRequestFilterCollapse">
        <div class="premium-leaverequest-filter-body">
            <form method="get" novalidate id="leaveRequestFilterForm">
                <div class="row g-4">
                    {# Loop through visible fields of the filter form #}
                    {% for field in filter_form.visible_fields %}
                        <div class="col-md-{% if filter_form.visible_fields|length > 4 %}3{% elif filter_form.visible_fields|length == 1 %}12{% elif filter_form.visible_fields|length == 2 %}6{% else %}4{% endif %}">
                            <div class="form-floating-leaverequest">
                                {% if field.field.widget.input_type == 'select' %}
                                    {% render_field field class+="form-select" placeholder=field.label %}
                                    <i class="premium-leaverequest-field-icon bi bi-chevron-down"></i>
                                {% elif field.field.widget.input_type == 'date' %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-leaverequest-field-icon bi bi-calendar3"></i>
                                {% elif field.field.widget.input_type == 'number' %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-leaverequest-field-icon bi bi-123"></i>
                                {% elif field.field.widget.input_type == 'text' %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-leaverequest-field-icon bi bi-search"></i>
                                {% else %}
                                    {% render_field field class+="form-control" placeholder=field.label %}
                                    <i class="premium-leaverequest-field-icon bi bi-calendar-check"></i>
                                {% endif %}
                                <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                                {% if field.help_text %}
                                    <small class="form-text text-muted mt-1">{{ field.help_text }}</small>
                                {% endif %}
                            </div>
                            {% for error in field.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="bi bi-exclamation-triangle me-1"></i>{{ error }}
                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                </div>

                {# Action Buttons #}
                <div class="leaverequest-actions-container">
                    <div class="row g-3 justify-content-end">
                        <div class="col-auto">
                            <a href="{{ request.path }}" class="btn btn-leaverequest-premium btn-leaverequest-premium-secondary">
                                <i class="bi bi-arrow-clockwise me-2"></i>{% trans "Reset Filters" %}
                            </a>
                        </div>
                        <div class="col-auto">
                            <button type="submit" class="btn btn-leaverequest-premium btn-leaverequest-premium-primary" id="applyLeaveRequestFiltersBtn">
                                <i class="bi bi-search me-2"></i>{% trans "Apply Filters" %}
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            {% if filter_form.non_field_errors %}
                <div class="alert alert-danger mt-3">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    {% for error in filter_form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}
