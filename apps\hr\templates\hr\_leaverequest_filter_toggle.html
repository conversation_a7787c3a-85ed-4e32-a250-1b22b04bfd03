﻿{# apps/hr/templates/hr/_leaverequest_filter_toggle.html - Leave Request List Toggle Filter #}
{% load i18n widget_tweaks %}

{% if filter_form %}
<!-- Premium Leave Request Filter -->
<div class="card shadow-sm mb-4" style="border-radius: 1rem; overflow: hidden;">
    <div class="card-header" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); color: white; padding: 1.5rem;">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-calendar-check-fill me-2"></i>Leave Request Filters & Data Management
            </h5>
            <div class="d-flex align-items-center gap-2">
                <a href="?export=csv" class="btn btn-sm btn-light">
                    <i class="bi bi-file-earmark-spreadsheet me-1"></i>CSV
                </a>
                <a href="?export=excel" class="btn btn-sm btn-light">
                    <i class="bi bi-file-earmark-excel me-1"></i>Excel
                </a>
                <a href="?export=pdf" class="btn btn-sm btn-light">
                    <i class="bi bi-file-earmark-pdf me-1"></i>PDF
                </a>
                <button class="btn btn-outline-light btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#leaveRequestFilterCollapse">
                    <i class="bi bi-chevron-down me-1"></i>Toggle Filters
                </button>
            </div>
        </div>
    </div>
    
    <div class="collapse {% if request.GET and not request.GET.export %}show{% endif %}" id="leaveRequestFilterCollapse">
        <div class="card-body" style="padding: 2rem;">
            <form method="get" novalidate>
                <div class="row g-4">
                    {% for field in filter_form.visible_fields %}
                        <div class="col-md-{% if filter_form.visible_fields|length > 4 %}3{% elif filter_form.visible_fields|length == 1 %}12{% elif filter_form.visible_fields|length == 2 %}6{% else %}4{% endif %}">
                            <div class="form-floating">
                                {% if field.field.widget.input_type == 'select' %}
                                    {{ field|add_class:"form-select" }}
                                {% elif field.field.widget.input_type == 'date' %}
                                    {{ field|add_class:"form-control" }}
                                {% else %}
                                    {{ field|add_class:"form-control" }}
                                {% endif %}
                                <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                            </div>
                            {% if field.help_text %}
                                <small class="form-text text-muted mt-1">{{ field.help_text }}</small>
                            {% endif %}
                            {% for error in field.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="bi bi-exclamation-triangle me-1"></i>{{ error }}
                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4 pt-3" style="border-top: 1px solid #dee2e6;">
                    <a href="{{ request.path }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise me-2"></i>Reset Filters
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-search me-2"></i>Apply Filters
                    </button>
                </div>
            </form>

            {% if filter_form.non_field_errors %}
                <div class="alert alert-danger mt-3">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    {% for error in filter_form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}
